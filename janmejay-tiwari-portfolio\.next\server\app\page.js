/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6654e010055e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcb2tydHdcXERlc2t0b3BcXFBvcnRmb2xpb1xcamFubWVqYXktdGl3YXJpLXBvcnRmb2xpb1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY2NTRlMDEwMDU1ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Janmejay Tiwari - Portfolio\",\n    description: \"A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans bg-background text-textPrimary antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZpQjtBQU9oQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdULDhLQUFjLENBQUMscURBQXFELENBQUM7c0JBRWxGSzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxva3J0d1xcRGVza3RvcFxcUG9ydGZvbGlvXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoe1xuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbiAgdmFyaWFibGU6IFwiLS1mb250LWludGVyXCIsXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiSmFubWVqYXkgVGl3YXJpIC0gUG9ydGZvbGlvXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkEgc2tpbGxlZCBhbmQgcGFzc2lvbmF0ZSBkZXZlbG9wZXIgd2l0aCBleHBlcmllbmNlIGluIGNyZWF0aW5nIG1vZGVybiwgcmVzcG9uc2l2ZSwgYW5kIHNjYWxhYmxlIHdlYiBhcHBsaWNhdGlvbnMgdXNpbmcgUmVhY3QsIE5leHQuanMsIGFuZCBBSS9NTCB0ZWNobm9sb2dpZXMuXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtpbnRlci52YXJpYWJsZX0gZm9udC1zYW5zIGJnLWJhY2tncm91bmQgdGV4dC10ZXh0UHJpbWFyeSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Portfolio\\janmejay-tiwari-portfolio\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q29rcnR3JTVDRGVza3RvcCU1Q1BvcnRmb2xpbyU1Q2phbm1lamF5LXRpd2FyaS1wb3J0Zm9saW8lNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q29rcnR3JTVDRGVza3RvcCU1Q1BvcnRmb2xpbyU1Q2phbm1lamF5LXRpd2FyaS1wb3J0Zm9saW8maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QmaXNHbG9iYWxOb3RGb3VuZEVuYWJsZWQ9ISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0IsNElBQXFIO0FBQzNJLHNCQUFzQix1T0FBd0Y7QUFDOUcsc0JBQXNCLGlPQUFxRjtBQUMzRyxzQkFBc0IsaU9BQXFGO0FBQzNHLHNCQUFzQix1T0FBd0Y7QUFDOUcsb0JBQW9CLHdJQUFtSDtBQUdySTtBQUdBO0FBQzJFO0FBQ0w7QUFDVDtBQUNPO0FBQ087QUFDTztBQUNQO0FBQ0s7QUFDWTtBQUNXO0FBQ3hCO0FBQ0Y7QUFDYTtBQUNpRTtBQUNoRjtBQUNYO0FBQ1E7QUFDaEI7QUFDdUI7QUFDUDtBQUNUO0FBQ2lCO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0Esb0NBQW9DLDhlQUE2UTtBQUNqVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyw4ZUFBNlE7QUFDalQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDcUI7QUFDdkIsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDb0Y7QUFHcEY7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLGFBQWEsT0FBb0MsSUFBSSxDQUFFO0FBQ3ZELGdCQUFnQixNQUF1QztBQUN2RCxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxLQUFxQixFQUFFLEVBRTFCLENBQUM7QUFDTjtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IsT0FBd0M7QUFDdkUsNkJBQTZCLDZFQUFjO0FBQzNDO0FBQ0Esd0JBQXdCLDZFQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHFTQUFxUztBQUNqVDtBQUNBLDhCQUE4Qiw4RkFBZ0I7QUFDOUMsVUFBVSx1QkFBdUI7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IscUZBQVU7QUFDOUIsc0JBQXNCLDBGQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsNkVBQWMscURBQXFELHdHQUEyQjtBQUNqSTtBQUNBLHlCQUF5Qiw2RUFBYyw2Q0FBNkMsdUZBQVU7QUFDOUYsbUNBQW1DLDJHQUF5QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsMkZBQW9CO0FBQ2xEO0FBQ0E7QUFDQSxxQ0FBcUMsTUFBNEcsSUFBSSxDQUFlO0FBQ3BLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0Esa0NBQWtDLDZFQUFjO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELHNHQUE0QjtBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvRUFBUztBQUNwQjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDZHQUE4QjtBQUN0QztBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsZ0dBQXFCO0FBQ2xEO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0EsbUJBQW1CLDRFQUFTO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw0RUFBZTtBQUMvQyxnQ0FBZ0MsNkVBQWdCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixJQUFzQztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsZ0ZBQWM7QUFDL0UsK0RBQStELHlDQUF5QztBQUN4RztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxRQUFRLEVBQUUsTUFBTTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGtCQUFrQjtBQUNsQix1Q0FBdUMsUUFBUSxFQUFFLFFBQVE7QUFDekQ7QUFDQSxhQUFhO0FBQ2I7QUFDQSxrQ0FBa0Msc0NBQXNDO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQiwwQ0FBMEMsNkVBQWM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQSwrQkFBK0IsMkZBQWM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsNkVBQWM7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLElBQUk7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLDRDQUE0QztBQUM1QztBQUNBLHlCQUF5Qiw2RUFBYztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixXQUFXO0FBQy9CLG9CQUFvQiwwQkFBMEI7QUFDOUMsbUNBQW1DO0FBQ25DO0FBQ0Esd0JBQXdCLDRFQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4R0FBOEcsaUJBQWlCLEVBQUUsb0ZBQW9GLDhCQUE4QixPQUFPO0FBQzFQO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZFQUFlO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsdURBQXVEO0FBQ2xHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLDJFQUFrQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpRUFBWSxjQUFjLGdGQUFLO0FBQ2hFLCtCQUErQixpRUFBWTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxpRUFBWTtBQUN0RSwrQkFBK0IsaUVBQVk7QUFDM0M7QUFDQSxpREFBaUQsaUVBQVk7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpRUFBWTtBQUM3Qyw4QkFBOEIsNkZBQWU7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGtFQUFTO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RUFBdUUsZ0dBQXNCO0FBQzdGLDZCQUE2QjtBQUM3QjtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSw4QkFBOEIsNkVBQWU7QUFDN0MsOEJBQThCLHVFQUFZO0FBQzFDLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0VBQStFLDZFQUFjLHdEQUF3RCxnR0FBc0I7QUFDM0s7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLDJCQUEyQixrRUFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLHVHQUF1Ryw2RUFBZTtBQUN0SDtBQUNBLGlIQUFpSCxtRkFBbUY7QUFDcE07QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHFHQUF3QjtBQUN0RDtBQUNBLG9CQUFvQixvQkFBb0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdIQUFnSCxvQ0FBb0M7QUFDcEo7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSx3Q0FBd0Msb0VBQWM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUhBQWlILDZFQUFlO0FBQ2hJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHFHQUF3QjtBQUN0RDtBQUNBO0FBQ0EsaUhBQWlILDRFQUFzQjtBQUN2STtBQUNBLGtDQUFrQyw0RUFBc0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsZ0ZBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsdUVBQVk7QUFDNUM7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix1RUFBWTtBQUN4QztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsNkVBQWM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHlCQUF5Qiw2RUFBYztBQUN2QyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw0RUFBc0I7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyR0FBMkcsNEVBQXNCO0FBQ2pJO0FBQ0EsOEJBQThCLDRFQUFzQjtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELGlHQUFrQjtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixxR0FBd0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQSwyQkFBMkIsZ0ZBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix1RUFBWTtBQUN4QztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnRkFBZ0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMscUZBQVk7QUFDdkQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQix1QkFBdUIsZ0ZBQWdCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSwrRkFBK0YsNkVBQWU7QUFDOUc7QUFDQSxzR0FBc0csdUVBQXVFO0FBQzdLO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiLG1CQUFtQixnRkFBZ0I7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixvRkFBb0YsZ0ZBQWM7QUFDbEcsaUNBQWlDLFFBQVEsRUFBRSxRQUFRO0FBQ25ELDBCQUEwQix1RUFBUTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLE1BQU07QUFDTjtBQUNBLDRDQUE0Qyw2RkFBZTtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywyRkFBbUI7QUFDckQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG9rcnR3XFxcXERlc2t0b3BcXFxcUG9ydGZvbGlvXFxcXGphbm1lamF5LXRpd2FyaS1wb3J0Zm9saW9cXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG5jb25zdCBtb2R1bGUxID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9nbG9iYWwtZXJyb3IuanNcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9ub3QtZm91bmQuanNcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9mb3JiaWRkZW4uanNcIik7XG5jb25zdCBtb2R1bGU0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi91bmF1dGhvcml6ZWQuanNcIik7XG5jb25zdCBwYWdlNSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb2tydHdcXFxcRGVza3RvcFxcXFxQb3J0Zm9saW9cXFxcamFubWVqYXktdGl3YXJpLXBvcnRmb2xpb1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuaW1wb3J0IHsgZ2V0UmV2YWxpZGF0ZVJlYXNvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2luc3RydW1lbnRhdGlvbi91dGlsc1wiO1xuaW1wb3J0IHsgZ2V0VHJhY2VyLCBTcGFuS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS90cmFjZXJcIjtcbmltcG9ydCB7IGdldFJlcXVlc3RNZXRhIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVxdWVzdC1tZXRhXCI7XG5pbXBvcnQgeyBCYXNlU2VydmVyU3BhbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi90cmFjZS9jb25zdGFudHNcIjtcbmltcG9ydCB7IGludGVyb3BEZWZhdWx0IH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9pbnRlcm9wLWRlZmF1bHRcIjtcbmltcG9ydCB7IE5vZGVOZXh0UmVxdWVzdCwgTm9kZU5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Jhc2UtaHR0cC9ub2RlXCI7XG5pbXBvcnQgeyBjaGVja0lzQXBwUFBSRW5hYmxlZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9leHBlcmltZW50YWwvcHByXCI7XG5pbXBvcnQgeyBnZXRGYWxsYmFja1JvdXRlUGFyYW1zIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVxdWVzdC9mYWxsYmFjay1wYXJhbXNcIjtcbmltcG9ydCB7IHNldFJlZmVyZW5jZU1hbmlmZXN0c1NpbmdsZXRvbiB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW5jcnlwdGlvbi11dGlsc1wiO1xuaW1wb3J0IHsgaXNIdG1sQm90UmVxdWVzdCwgc2hvdWxkU2VydmVTdHJlYW1pbmdNZXRhZGF0YSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9zdHJlYW1pbmctbWV0YWRhdGFcIjtcbmltcG9ydCB7IGNyZWF0ZVNlcnZlck1vZHVsZU1hcCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWN0aW9uLXV0aWxzXCI7XG5pbXBvcnQgeyBub3JtYWxpemVBcHBQYXRoIH0gZnJvbSBcIm5leHQvZGlzdC9zaGFyZWQvbGliL3JvdXRlci91dGlscy9hcHAtcGF0aHNcIjtcbmltcG9ydCB7IGdldElzUG9zc2libGVTZXJ2ZXJBY3Rpb24gfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvc2VydmVyLWFjdGlvbi1yZXF1ZXN0LW1ldGFcIjtcbmltcG9ydCB7IFJTQ19IRUFERVIsIE5FWFRfUk9VVEVSX1BSRUZFVENIX0hFQURFUiwgTkVYVF9JU19QUkVSRU5ERVJfSEVBREVSLCBORVhUX0RJRF9QT1NUUE9ORV9IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXItaGVhZGVyc1wiO1xuaW1wb3J0IHsgZ2V0Qm90VHlwZSwgaXNCb3QgfSBmcm9tIFwibmV4dC9kaXN0L3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2lzLWJvdFwiO1xuaW1wb3J0IHsgQ2FjaGVkUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVzcG9uc2UtY2FjaGVcIjtcbmltcG9ydCB7IEZhbGxiYWNrTW9kZSwgcGFyc2VGYWxsYmFja0ZpZWxkIH0gZnJvbSBcIm5leHQvZGlzdC9saWIvZmFsbGJhY2tcIjtcbmltcG9ydCBSZW5kZXJSZXN1bHQgZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcmVuZGVyLXJlc3VsdFwiO1xuaW1wb3J0IHsgQ0FDSEVfT05FX1lFQVIsIE5FWFRfQ0FDSEVfVEFHU19IRUFERVIgfSBmcm9tIFwibmV4dC9kaXN0L2xpYi9jb25zdGFudHNcIjtcbmltcG9ydCB7IEVOQ09ERURfVEFHUyB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3N0cmVhbS11dGlscy9lbmNvZGVkLXRhZ3NcIjtcbmltcG9ydCB7IHNlbmRSZW5kZXJSZXN1bHQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9zZW5kLXBheWxvYWRcIjtcbmltcG9ydCB7IE5vRmFsbGJhY2tFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3Qvc2hhcmVkL2xpYi9uby1mYWxsYmFjay1lcnJvci5leHRlcm5hbFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNSwgXCJDOlxcXFxVc2Vyc1xcXFxva3J0d1xcXFxEZXNrdG9wXFxcXFBvcnRmb2xpb1xcXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxcb2tydHdcXFxcRGVza3RvcFxcXFxQb3J0Zm9saW9cXFxcamFubWVqYXktdGl3YXJpLXBvcnRmb2xpb1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbbW9kdWxlMCwgXCJDOlxcXFxVc2Vyc1xcXFxva3J0d1xcXFxEZXNrdG9wXFxcXFBvcnRmb2xpb1xcXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ2dsb2JhbC1lcnJvcic6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL2dsb2JhbC1lcnJvci5qc1wiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9ub3QtZm91bmQuanNcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vZm9yYmlkZGVuLmpzXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGU0LCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9idWlsdGluL3VuYXV0aG9yaXplZC5qc1wiXSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICBpY29uOiBbKGFzeW5jIChwcm9wcykgPT4gKGF3YWl0IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXI/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIUM6XFxcXFVzZXJzXFxcXG9rcnR3XFxcXERlc2t0b3BcXFxcUG9ydGZvbGlvXFxcXGphbm1lamF5LXRpd2FyaS1wb3J0Zm9saW9cXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxcVXNlcnNcXFxcb2tydHdcXFxcRGVza3RvcFxcXFxQb3J0Zm9saW9cXFxcamFubWVqYXktdGl3YXJpLXBvcnRmb2xpb1xcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuaW1wb3J0IEdsb2JhbEVycm9yIGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9nbG9iYWwtZXJyb3IuanNcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5leHBvcnQgeyBHbG9iYWxFcnJvciB9O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5pbXBvcnQgKiBhcyBlbnRyeUJhc2UgZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuaW1wb3J0IHsgUmVkaXJlY3RTdGF0dXNDb2RlIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9yZWRpcmVjdC1zdGF0dXMtY29kZVwiO1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfSxcbiAgICBkaXN0RGlyOiBwcm9jZXNzLmVudi5fX05FWFRfUkVMQVRJVkVfRElTVF9ESVIgfHwgJycsXG4gICAgcHJvamVjdERpcjogcHJvY2Vzcy5lbnYuX19ORVhUX1JFTEFUSVZFX1BST0pFQ1RfRElSIHx8ICcnXG59KTtcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYW5kbGVyKHJlcSwgcmVzLCBjdHgpIHtcbiAgICB2YXIgX3RoaXM7XG4gICAgbGV0IHNyY1BhZ2UgPSBcIi9wYWdlXCI7XG4gICAgLy8gdHVyYm9wYWNrIGRvZXNuJ3Qgbm9ybWFsaXplIGAvaW5kZXhgIGluIHRoZSBwYWdlIG5hbWVcbiAgICAvLyBzbyB3ZSBuZWVkIHRvIHRvIHByb2Nlc3MgZHluYW1pYyByb3V0ZXMgcHJvcGVybHlcbiAgICAvLyBUT0RPOiBmaXggdHVyYm9wYWNrIHByb3ZpZGluZyBkaWZmZXJpbmcgdmFsdWUgZnJvbSB3ZWJwYWNrXG4gICAgaWYgKHByb2Nlc3MuZW52LlRVUkJPUEFDSykge1xuICAgICAgICBzcmNQYWdlID0gc3JjUGFnZS5yZXBsYWNlKC9cXC9pbmRleCQvLCAnJykgfHwgJy8nO1xuICAgIH0gZWxzZSBpZiAoc3JjUGFnZSA9PT0gJy9pbmRleCcpIHtcbiAgICAgICAgLy8gd2UgYWx3YXlzIG5vcm1hbGl6ZSAvaW5kZXggc3BlY2lmaWNhbGx5XG4gICAgICAgIHNyY1BhZ2UgPSAnLyc7XG4gICAgfVxuICAgIGNvbnN0IG11bHRpWm9uZURyYWZ0TW9kZSA9IHByb2Nlc3MuZW52Ll9fTkVYVF9NVUxUSV9aT05FX0RSQUZUX01PREU7XG4gICAgY29uc3QgaW5pdGlhbFBvc3Rwb25lZCA9IGdldFJlcXVlc3RNZXRhKHJlcSwgJ3Bvc3Rwb25lZCcpO1xuICAgIC8vIFRPRE86IHJlcGxhY2Ugd2l0aCBtb3JlIHNwZWNpZmljIGZsYWdzXG4gICAgY29uc3QgbWluaW1hbE1vZGUgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdtaW5pbWFsTW9kZScpO1xuICAgIGNvbnN0IHByZXBhcmVSZXN1bHQgPSBhd2FpdCByb3V0ZU1vZHVsZS5wcmVwYXJlKHJlcSwgcmVzLCB7XG4gICAgICAgIHNyY1BhZ2UsXG4gICAgICAgIG11bHRpWm9uZURyYWZ0TW9kZVxuICAgIH0pO1xuICAgIGlmICghcHJlcGFyZVJlc3VsdCkge1xuICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDQwMDtcbiAgICAgICAgcmVzLmVuZCgnQmFkIFJlcXVlc3QnKTtcbiAgICAgICAgY3R4LndhaXRVbnRpbCA9PSBudWxsID8gdm9pZCAwIDogY3R4LndhaXRVbnRpbC5jYWxsKGN0eCwgUHJvbWlzZS5yZXNvbHZlKCkpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgY29uc3QgeyBidWlsZElkLCBxdWVyeSwgcGFyYW1zLCBwYXJzZWRVcmwsIHBhZ2VJc0R5bmFtaWMsIGJ1aWxkTWFuaWZlc3QsIG5leHRGb250TWFuaWZlc3QsIHJlYWN0TG9hZGFibGVNYW5pZmVzdCwgc2VydmVyQWN0aW9uc01hbmlmZXN0LCBjbGllbnRSZWZlcmVuY2VNYW5pZmVzdCwgc3VicmVzb3VyY2VJbnRlZ3JpdHlNYW5pZmVzdCwgcHJlcmVuZGVyTWFuaWZlc3QsIGlzRHJhZnRNb2RlLCByZXNvbHZlZFBhdGhuYW1lLCByZXZhbGlkYXRlT25seUdlbmVyYXRlZCwgcm91dGVyU2VydmVyQ29udGV4dCwgbmV4dENvbmZpZyB9ID0gcHJlcGFyZVJlc3VsdDtcbiAgICBjb25zdCBwYXRobmFtZSA9IHBhcnNlZFVybC5wYXRobmFtZSB8fCAnLyc7XG4gICAgY29uc3Qgbm9ybWFsaXplZFNyY1BhZ2UgPSBub3JtYWxpemVBcHBQYXRoKHNyY1BhZ2UpO1xuICAgIGxldCB7IGlzT25EZW1hbmRSZXZhbGlkYXRlIH0gPSBwcmVwYXJlUmVzdWx0O1xuICAgIGNvbnN0IHByZXJlbmRlckluZm8gPSBwcmVyZW5kZXJNYW5pZmVzdC5keW5hbWljUm91dGVzW25vcm1hbGl6ZWRTcmNQYWdlXTtcbiAgICBjb25zdCBpc1ByZXJlbmRlcmVkID0gcHJlcmVuZGVyTWFuaWZlc3Qucm91dGVzW3Jlc29sdmVkUGF0aG5hbWVdO1xuICAgIGxldCBpc1NTRyA9IEJvb2xlYW4ocHJlcmVuZGVySW5mbyB8fCBpc1ByZXJlbmRlcmVkIHx8IHByZXJlbmRlck1hbmlmZXN0LnJvdXRlc1tub3JtYWxpemVkU3JjUGFnZV0pO1xuICAgIGNvbnN0IHVzZXJBZ2VudCA9IHJlcS5oZWFkZXJzWyd1c2VyLWFnZW50J10gfHwgJyc7XG4gICAgY29uc3QgYm90VHlwZSA9IGdldEJvdFR5cGUodXNlckFnZW50KTtcbiAgICBjb25zdCBpc0h0bWxCb3QgPSBpc0h0bWxCb3RSZXF1ZXN0KHJlcSk7XG4gICAgLyoqXG4gICAqIElmIHRydWUsIHRoaXMgaW5kaWNhdGVzIHRoYXQgdGhlIHJlcXVlc3QgYmVpbmcgbWFkZSBpcyBmb3IgYW4gYXBwXG4gICAqIHByZWZldGNoIHJlcXVlc3QuXG4gICAqLyBjb25zdCBpc1ByZWZldGNoUlNDUmVxdWVzdCA9IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2lzUHJlZmV0Y2hSU0NSZXF1ZXN0JykgPz8gQm9vbGVhbihyZXEuaGVhZGVyc1tORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVJdKTtcbiAgICAvLyBOT1RFOiBEb24ndCBkZWxldGUgaGVhZGVyc1tSU0NdIHlldCwgaXQgc3RpbGwgbmVlZHMgdG8gYmUgdXNlZCBpbiByZW5kZXJUb0hUTUwgbGF0ZXJcbiAgICBjb25zdCBpc1JTQ1JlcXVlc3QgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdpc1JTQ1JlcXVlc3QnKSA/PyBCb29sZWFuKHJlcS5oZWFkZXJzW1JTQ19IRUFERVJdKTtcbiAgICBjb25zdCBpc1Bvc3NpYmxlU2VydmVyQWN0aW9uID0gZ2V0SXNQb3NzaWJsZVNlcnZlckFjdGlvbihyZXEpO1xuICAgIC8qKlxuICAgKiBJZiB0aGUgcm91dGUgYmVpbmcgcmVuZGVyZWQgaXMgYW4gYXBwIHBhZ2UsIGFuZCB0aGUgcHByIGZlYXR1cmUgaGFzIGJlZW5cbiAgICogZW5hYmxlZCwgdGhlbiB0aGUgZ2l2ZW4gcm91dGUgX2NvdWxkXyBzdXBwb3J0IFBQUi5cbiAgICovIGNvbnN0IGNvdWxkU3VwcG9ydFBQUiA9IGNoZWNrSXNBcHBQUFJFbmFibGVkKG5leHRDb25maWcuZXhwZXJpbWVudGFsLnBwcik7XG4gICAgLy8gV2hlbiBlbmFibGVkLCB0aGlzIHdpbGwgYWxsb3cgdGhlIHVzZSBvZiB0aGUgYD9fX25leHRwcHJvbmx5YCBxdWVyeSB0b1xuICAgIC8vIGVuYWJsZSBkZWJ1Z2dpbmcgb2YgdGhlIHN0YXRpYyBzaGVsbC5cbiAgICBjb25zdCBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgPSBwcm9jZXNzLmVudi5fX05FWFRfRVhQRVJJTUVOVEFMX1NUQVRJQ19TSEVMTF9ERUJVR0dJTkcgPT09ICcxJyAmJiB0eXBlb2YgcXVlcnkuX19uZXh0cHByb25seSAhPT0gJ3VuZGVmaW5lZCcgJiYgY291bGRTdXBwb3J0UFBSO1xuICAgIC8vIFdoZW4gZW5hYmxlZCwgdGhpcyB3aWxsIGFsbG93IHRoZSB1c2Ugb2YgdGhlIGA/X19uZXh0cHByb25seWAgcXVlcnlcbiAgICAvLyB0byBlbmFibGUgZGVidWdnaW5nIG9mIHRoZSBmYWxsYmFjayBzaGVsbC5cbiAgICBjb25zdCBoYXNEZWJ1Z0ZhbGxiYWNrU2hlbGxRdWVyeSA9IGhhc0RlYnVnU3RhdGljU2hlbGxRdWVyeSAmJiBxdWVyeS5fX25leHRwcHJvbmx5ID09PSAnZmFsbGJhY2snO1xuICAgIC8vIFRoaXMgcGFnZSBzdXBwb3J0cyBQUFIgaWYgaXQgaXMgbWFya2VkIGFzIGJlaW5nIGBQQVJUSUFMTFlfU1RBVElDYCBpbiB0aGVcbiAgICAvLyBwcmVyZW5kZXIgbWFuaWZlc3QgYW5kIHRoaXMgaXMgYW4gYXBwIHBhZ2UuXG4gICAgY29uc3QgaXNSb3V0ZVBQUkVuYWJsZWQgPSBjb3VsZFN1cHBvcnRQUFIgJiYgKCgoX3RoaXMgPSBwcmVyZW5kZXJNYW5pZmVzdC5yb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdID8/IHByZXJlbmRlck1hbmlmZXN0LmR5bmFtaWNSb3V0ZXNbbm9ybWFsaXplZFNyY1BhZ2VdKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMucmVuZGVyaW5nTW9kZSkgPT09ICdQQVJUSUFMTFlfU1RBVElDJyB8fCAvLyBJZGVhbGx5IHdlJ2Qgd2FudCB0byBjaGVjayB0aGUgYXBwQ29uZmlnIHRvIHNlZSBpZiB0aGlzIHBhZ2UgaGFzIFBQUlxuICAgIC8vIGVuYWJsZWQgb3Igbm90LCBidXQgdGhhdCB3b3VsZCByZXF1aXJlIHBsdW1iaW5nIHRoZSBhcHBDb25maWcgdGhyb3VnaFxuICAgIC8vIHRvIHRoZSBzZXJ2ZXIgZHVyaW5nIGRldmVsb3BtZW50LiBXZSBhc3N1bWUgdGhhdCB0aGUgcGFnZSBzdXBwb3J0cyBpdFxuICAgIC8vIGJ1dCBvbmx5IGR1cmluZyBkZXZlbG9wbWVudC5cbiAgICBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgJiYgKHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlIHx8IChyb3V0ZXJTZXJ2ZXJDb250ZXh0ID09IG51bGwgPyB2b2lkIDAgOiByb3V0ZXJTZXJ2ZXJDb250ZXh0LmV4cGVyaW1lbnRhbFRlc3RQcm94eSkgPT09IHRydWUpKTtcbiAgICBjb25zdCBpc0RlYnVnU3RhdGljU2hlbGwgPSBoYXNEZWJ1Z1N0YXRpY1NoZWxsUXVlcnkgJiYgaXNSb3V0ZVBQUkVuYWJsZWQ7XG4gICAgLy8gV2Ugc2hvdWxkIGVuYWJsZSBkZWJ1Z2dpbmcgZHluYW1pYyBhY2Nlc3NlcyB3aGVuIHRoZSBzdGF0aWMgc2hlbGxcbiAgICAvLyBkZWJ1Z2dpbmcgaGFzIGJlZW4gZW5hYmxlZCBhbmQgd2UncmUgYWxzbyBpbiBkZXZlbG9wbWVudCBtb2RlLlxuICAgIGNvbnN0IGlzRGVidWdEeW5hbWljQWNjZXNzZXMgPSBpc0RlYnVnU3RhdGljU2hlbGwgJiYgcm91dGVNb2R1bGUuaXNEZXYgPT09IHRydWU7XG4gICAgY29uc3QgaXNEZWJ1Z0ZhbGxiYWNrU2hlbGwgPSBoYXNEZWJ1Z0ZhbGxiYWNrU2hlbGxRdWVyeSAmJiBpc1JvdXRlUFBSRW5hYmxlZDtcbiAgICAvLyBJZiB3ZSdyZSBpbiBtaW5pbWFsIG1vZGUsIHRoZW4gdHJ5IHRvIGdldCB0aGUgcG9zdHBvbmVkIGluZm9ybWF0aW9uIGZyb21cbiAgICAvLyB0aGUgcmVxdWVzdCBtZXRhZGF0YS4gSWYgYXZhaWxhYmxlLCB1c2UgaXQgZm9yIHJlc3VtaW5nIHRoZSBwb3N0cG9uZWRcbiAgICAvLyByZW5kZXIuXG4gICAgY29uc3QgbWluaW1hbFBvc3Rwb25lZCA9IGlzUm91dGVQUFJFbmFibGVkID8gaW5pdGlhbFBvc3Rwb25lZCA6IHVuZGVmaW5lZDtcbiAgICAvLyBJZiBQUFIgaXMgZW5hYmxlZCwgYW5kIHRoaXMgaXMgYSBSU0MgcmVxdWVzdCAoYnV0IG5vdCBhIHByZWZldGNoKSwgdGhlblxuICAgIC8vIHdlIGNhbiB1c2UgdGhpcyBmYWN0IHRvIG9ubHkgZ2VuZXJhdGUgdGhlIGZsaWdodCBkYXRhIGZvciB0aGUgcmVxdWVzdFxuICAgIC8vIGJlY2F1c2Ugd2UgY2FuJ3QgY2FjaGUgdGhlIEhUTUwgKGFzIGl0J3MgYWxzbyBkeW5hbWljKS5cbiAgICBjb25zdCBpc0R5bmFtaWNSU0NSZXF1ZXN0ID0gaXNSb3V0ZVBQUkVuYWJsZWQgJiYgaXNSU0NSZXF1ZXN0ICYmICFpc1ByZWZldGNoUlNDUmVxdWVzdDtcbiAgICAvLyBOZWVkIHRvIHJlYWQgdGhpcyBiZWZvcmUgaXQncyBzdHJpcHBlZCBieSBzdHJpcEZsaWdodEhlYWRlcnMuIFdlIGRvbid0XG4gICAgLy8gbmVlZCB0byB0cmFuc2ZlciBpdCB0byB0aGUgcmVxdWVzdCBtZXRhIGJlY2F1c2UgaXQncyBvbmx5IHJlYWRcbiAgICAvLyB3aXRoaW4gdGhpcyBmdW5jdGlvbjsgdGhlIHN0YXRpYyBzZWdtZW50IGRhdGEgc2hvdWxkIGhhdmUgYWxyZWFkeSBiZWVuXG4gICAgLy8gZ2VuZXJhdGVkLCBzbyB3ZSB3aWxsIGFsd2F5cyBlaXRoZXIgcmV0dXJuIGEgc3RhdGljIHJlc3BvbnNlIG9yIGEgNDA0LlxuICAgIGNvbnN0IHNlZ21lbnRQcmVmZXRjaEhlYWRlciA9IGdldFJlcXVlc3RNZXRhKHJlcSwgJ3NlZ21lbnRQcmVmZXRjaFJTQ1JlcXVlc3QnKTtcbiAgICAvLyBUT0RPOiBpbnZlc3RpZ2F0ZSBleGlzdGluZyBidWcgd2l0aCBzaG91bGRTZXJ2ZVN0cmVhbWluZ01ldGFkYXRhIGFsd2F5c1xuICAgIC8vIGJlaW5nIHRydWUgZm9yIGEgcmV2YWxpZGF0ZSBkdWUgdG8gbW9kaWZ5aW5nIHRoZSBiYXNlLXNlcnZlciB0aGlzLnJlbmRlck9wdHNcbiAgICAvLyB3aGVuIGZpeGluZyB0aGlzIHRvIGNvcnJlY3QgbG9naWMgaXQgY2F1c2VzIGh5ZHJhdGlvbiBpc3N1ZSBzaW5jZSB3ZSBzZXRcbiAgICAvLyBzZXJ2ZVN0cmVhbWluZ01ldGFkYXRhIHRvIHRydWUgZHVyaW5nIGV4cG9ydFxuICAgIGxldCBzZXJ2ZVN0cmVhbWluZ01ldGFkYXRhID0gIXVzZXJBZ2VudCA/IHRydWUgOiBzaG91bGRTZXJ2ZVN0cmVhbWluZ01ldGFkYXRhKHVzZXJBZ2VudCwgbmV4dENvbmZpZy5odG1sTGltaXRlZEJvdHMpO1xuICAgIGlmIChpc0h0bWxCb3QgJiYgaXNSb3V0ZVBQUkVuYWJsZWQpIHtcbiAgICAgICAgaXNTU0cgPSBmYWxzZTtcbiAgICAgICAgc2VydmVTdHJlYW1pbmdNZXRhZGF0YSA9IGZhbHNlO1xuICAgIH1cbiAgICAvLyBJbiBkZXZlbG9wbWVudCwgd2UgYWx3YXlzIHdhbnQgdG8gZ2VuZXJhdGUgZHluYW1pYyBIVE1MLlxuICAgIGxldCBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSA9IC8vIElmIHdlJ3JlIGluIGRldmVsb3BtZW50LCB3ZSBhbHdheXMgc3VwcG9ydCBkeW5hbWljIEhUTUwsIHVubGVzcyBpdCdzXG4gICAgLy8gYSBkYXRhIHJlcXVlc3QsIGluIHdoaWNoIGNhc2Ugd2Ugb25seSBwcm9kdWNlIHN0YXRpYyBIVE1MLlxuICAgIHJvdXRlTW9kdWxlLmlzRGV2ID09PSB0cnVlIHx8IC8vIElmIHRoaXMgaXMgbm90IFNTRyBvciBkb2VzIG5vdCBoYXZlIHN0YXRpYyBwYXRocywgdGhlbiBpdCBzdXBwb3J0c1xuICAgIC8vIGR5bmFtaWMgSFRNTC5cbiAgICAhaXNTU0cgfHwgLy8gSWYgdGhpcyByZXF1ZXN0IGhhcyBwcm92aWRlZCBwb3N0cG9uZWQgZGF0YSwgaXQgc3VwcG9ydHMgZHluYW1pY1xuICAgIC8vIEhUTUwuXG4gICAgdHlwZW9mIGluaXRpYWxQb3N0cG9uZWQgPT09ICdzdHJpbmcnIHx8IC8vIElmIHRoaXMgaXMgYSBkeW5hbWljIFJTQyByZXF1ZXN0LCB0aGVuIHRoaXMgcmVuZGVyIHN1cHBvcnRzIGR5bmFtaWNcbiAgICAvLyBIVE1MIChpdCdzIGR5bmFtaWMpLlxuICAgIGlzRHluYW1pY1JTQ1JlcXVlc3Q7XG4gICAgLy8gV2hlbiBodG1sIGJvdHMgcmVxdWVzdCBQUFIgcGFnZSwgcGVyZm9ybSB0aGUgZnVsbCBkeW5hbWljIHJlbmRlcmluZy5cbiAgICBjb25zdCBzaG91bGRXYWl0T25BbGxSZWFkeSA9IGlzSHRtbEJvdCAmJiBpc1JvdXRlUFBSRW5hYmxlZDtcbiAgICBsZXQgc3NnQ2FjaGVLZXkgPSBudWxsO1xuICAgIGlmICghaXNEcmFmdE1vZGUgJiYgaXNTU0cgJiYgIXN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlICYmICFpc1Bvc3NpYmxlU2VydmVyQWN0aW9uICYmICFtaW5pbWFsUG9zdHBvbmVkICYmICFpc0R5bmFtaWNSU0NSZXF1ZXN0KSB7XG4gICAgICAgIHNzZ0NhY2hlS2V5ID0gcmVzb2x2ZWRQYXRobmFtZTtcbiAgICB9XG4gICAgLy8gdGhlIHN0YXRpY1BhdGhLZXkgZGlmZmVycyBmcm9tIHNzZ0NhY2hlS2V5IHNpbmNlXG4gICAgLy8gc3NnQ2FjaGVLZXkgaXMgbnVsbCBpbiBkZXYgc2luY2Ugd2UncmUgYWx3YXlzIGluIFwiZHluYW1pY1wiXG4gICAgLy8gbW9kZSBpbiBkZXYgdG8gYnlwYXNzIHRoZSBjYWNoZSwgYnV0IHdlIHN0aWxsIG5lZWQgdG8gaG9ub3JcbiAgICAvLyBkeW5hbWljUGFyYW1zID0gZmFsc2UgaW4gZGV2IG1vZGVcbiAgICBsZXQgc3RhdGljUGF0aEtleSA9IHNzZ0NhY2hlS2V5O1xuICAgIGlmICghc3RhdGljUGF0aEtleSAmJiByb3V0ZU1vZHVsZS5pc0Rldikge1xuICAgICAgICBzdGF0aWNQYXRoS2V5ID0gcmVzb2x2ZWRQYXRobmFtZTtcbiAgICB9XG4gICAgY29uc3QgQ29tcG9uZW50TW9kID0ge1xuICAgICAgICAuLi5lbnRyeUJhc2UsXG4gICAgICAgIHRyZWUsXG4gICAgICAgIHBhZ2VzLFxuICAgICAgICBHbG9iYWxFcnJvcixcbiAgICAgICAgaGFuZGxlcixcbiAgICAgICAgcm91dGVNb2R1bGUsXG4gICAgICAgIF9fbmV4dF9hcHBfX1xuICAgIH07XG4gICAgLy8gQmVmb3JlIHJlbmRlcmluZyAod2hpY2ggaW5pdGlhbGl6ZXMgY29tcG9uZW50IHRyZWUgbW9kdWxlcyksIHdlIGhhdmUgdG9cbiAgICAvLyBzZXQgdGhlIHJlZmVyZW5jZSBtYW5pZmVzdHMgdG8gb3VyIGdsb2JhbCBzdG9yZSBzbyBTZXJ2ZXIgQWN0aW9uJ3NcbiAgICAvLyBlbmNyeXB0aW9uIHV0aWwgY2FuIGFjY2VzcyB0byB0aGVtIGF0IHRoZSB0b3AgbGV2ZWwgb2YgdGhlIHBhZ2UgbW9kdWxlLlxuICAgIGlmIChzZXJ2ZXJBY3Rpb25zTWFuaWZlc3QgJiYgY2xpZW50UmVmZXJlbmNlTWFuaWZlc3QpIHtcbiAgICAgICAgc2V0UmVmZXJlbmNlTWFuaWZlc3RzU2luZ2xldG9uKHtcbiAgICAgICAgICAgIHBhZ2U6IHNyY1BhZ2UsXG4gICAgICAgICAgICBjbGllbnRSZWZlcmVuY2VNYW5pZmVzdCxcbiAgICAgICAgICAgIHNlcnZlckFjdGlvbnNNYW5pZmVzdCxcbiAgICAgICAgICAgIHNlcnZlck1vZHVsZU1hcDogY3JlYXRlU2VydmVyTW9kdWxlTWFwKHtcbiAgICAgICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zTWFuaWZlc3RcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBjb25zdCBtZXRob2QgPSByZXEubWV0aG9kIHx8ICdHRVQnO1xuICAgIGNvbnN0IHRyYWNlciA9IGdldFRyYWNlcigpO1xuICAgIGNvbnN0IGFjdGl2ZVNwYW4gPSB0cmFjZXIuZ2V0QWN0aXZlU2NvcGVTcGFuKCk7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3QgaW52b2tlUm91dGVNb2R1bGUgPSBhc3luYyAoc3BhbiwgY29udGV4dCk9PntcbiAgICAgICAgICAgIGNvbnN0IG5leHRSZXEgPSBuZXcgTm9kZU5leHRSZXF1ZXN0KHJlcSk7XG4gICAgICAgICAgICBjb25zdCBuZXh0UmVzID0gbmV3IE5vZGVOZXh0UmVzcG9uc2UocmVzKTtcbiAgICAgICAgICAgIC8vIFRPRE86IGFkYXB0IGZvciBwdXR0aW5nIHRoZSBSREMgaW5zaWRlIHRoZSBwb3N0cG9uZWQgZGF0YVxuICAgICAgICAgICAgLy8gSWYgd2UncmUgaW4gZGV2LCBhbmQgdGhpcyBpc24ndCBhIHByZWZldGNoIG9yIGEgc2VydmVyIGFjdGlvbixcbiAgICAgICAgICAgIC8vIHdlIHNob3VsZCBzZWVkIHRoZSByZXN1bWUgZGF0YSBjYWNoZS5cbiAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgICAgICAgICAgIGlmIChuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljSU8gJiYgIWlzUHJlZmV0Y2hSU0NSZXF1ZXN0ICYmICFjb250ZXh0LnJlbmRlck9wdHMuaXNQb3NzaWJsZVNlcnZlckFjdGlvbikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB3YXJtdXAgPSBhd2FpdCByb3V0ZU1vZHVsZS53YXJtdXAobmV4dFJlcSwgbmV4dFJlcywgY29udGV4dCk7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSB3YXJtdXAgaXMgc3VjY2Vzc2Z1bCwgd2Ugc2hvdWxkIHVzZSB0aGUgcmVzdW1lIGRhdGFcbiAgICAgICAgICAgICAgICAgICAgLy8gY2FjaGUgZnJvbSB0aGUgd2FybXVwLlxuICAgICAgICAgICAgICAgICAgICBpZiAod2FybXVwLm1ldGFkYXRhLnJlbmRlclJlc3VtZURhdGFDYWNoZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5yZW5kZXJPcHRzLnJlbmRlclJlc3VtZURhdGFDYWNoZSA9IHdhcm11cC5tZXRhZGF0YS5yZW5kZXJSZXN1bWVEYXRhQ2FjaGU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcm91dGVNb2R1bGUucmVuZGVyKG5leHRSZXEsIG5leHRSZXMsIGNvbnRleHQpLmZpbmFsbHkoKCk9PntcbiAgICAgICAgICAgICAgICBpZiAoIXNwYW4pIHJldHVybjtcbiAgICAgICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZXMoe1xuICAgICAgICAgICAgICAgICAgICAnaHR0cC5zdGF0dXNfY29kZSc6IHJlcy5zdGF0dXNDb2RlLFxuICAgICAgICAgICAgICAgICAgICAnbmV4dC5yc2MnOiBmYWxzZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJvb3RTcGFuQXR0cmlidXRlcyA9IHRyYWNlci5nZXRSb290U3BhbkF0dHJpYnV0ZXMoKTtcbiAgICAgICAgICAgICAgICAvLyBXZSB3ZXJlIHVuYWJsZSB0byBnZXQgYXR0cmlidXRlcywgcHJvYmFibHkgT1RFTCBpcyBub3QgZW5hYmxlZFxuICAgICAgICAgICAgICAgIGlmICghcm9vdFNwYW5BdHRyaWJ1dGVzKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQuc3Bhbl90eXBlJykgIT09IEJhc2VTZXJ2ZXJTcGFuLmhhbmRsZVJlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBVbmV4cGVjdGVkIHJvb3Qgc3BhbiB0eXBlICcke3Jvb3RTcGFuQXR0cmlidXRlcy5nZXQoJ25leHQuc3Bhbl90eXBlJyl9Jy4gUGxlYXNlIHJlcG9ydCB0aGlzIE5leHQuanMgaXNzdWUgaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzYCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgcm91dGUgPSByb290U3BhbkF0dHJpYnV0ZXMuZ2V0KCduZXh0LnJvdXRlJyk7XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5hbWUgPSBgJHttZXRob2R9ICR7cm91dGV9YDtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICduZXh0LnJvdXRlJzogcm91dGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAnaHR0cC5yb3V0ZSc6IHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ25leHQuc3Bhbl9uYW1lJzogbmFtZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi51cGRhdGVOYW1lKG5hbWUpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4udXBkYXRlTmFtZShgJHttZXRob2R9ICR7cmVxLnVybH1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgZG9SZW5kZXIgPSBhc3luYyAoeyBzcGFuLCBwb3N0cG9uZWQsIGZhbGxiYWNrUm91dGVQYXJhbXMgfSk9PntcbiAgICAgICAgICAgIGNvbnN0IGNvbnRleHQgPSB7XG4gICAgICAgICAgICAgICAgcXVlcnksXG4gICAgICAgICAgICAgICAgcGFyYW1zLFxuICAgICAgICAgICAgICAgIHBhZ2U6IG5vcm1hbGl6ZWRTcmNQYWdlLFxuICAgICAgICAgICAgICAgIHNoYXJlZENvbnRleHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgYnVpbGRJZFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgc2VydmVyQ29tcG9uZW50c0htckNhY2hlOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdzZXJ2ZXJDb21wb25lbnRzSG1yQ2FjaGUnKSxcbiAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zLFxuICAgICAgICAgICAgICAgIHJlbmRlck9wdHM6IHtcbiAgICAgICAgICAgICAgICAgICAgQXBwOiAoKT0+bnVsbCxcbiAgICAgICAgICAgICAgICAgICAgRG9jdW1lbnQ6ICgpPT5udWxsLFxuICAgICAgICAgICAgICAgICAgICBwYWdlQ29uZmlnOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgQ29tcG9uZW50TW9kLFxuICAgICAgICAgICAgICAgICAgICBDb21wb25lbnQ6IGludGVyb3BEZWZhdWx0KENvbXBvbmVudE1vZCksXG4gICAgICAgICAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgICAgICAgICAgICAgcm91dGVNb2R1bGUsXG4gICAgICAgICAgICAgICAgICAgIHBhZ2U6IHNyY1BhZ2UsXG4gICAgICAgICAgICAgICAgICAgIHBvc3Rwb25lZCxcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkV2FpdE9uQWxsUmVhZHksXG4gICAgICAgICAgICAgICAgICAgIHNlcnZlU3RyZWFtaW5nTWV0YWRhdGEsXG4gICAgICAgICAgICAgICAgICAgIHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlOiB0eXBlb2YgcG9zdHBvbmVkID09PSAnc3RyaW5nJyB8fCBzdXBwb3J0c0R5bmFtaWNSZXNwb25zZSxcbiAgICAgICAgICAgICAgICAgICAgYnVpbGRNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgbmV4dEZvbnRNYW5pZmVzdCxcbiAgICAgICAgICAgICAgICAgICAgcmVhY3RMb2FkYWJsZU1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBzdWJyZXNvdXJjZUludGVncml0eU1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBzZXJ2ZXJBY3Rpb25zTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgIGNsaWVudFJlZmVyZW5jZU1hbmlmZXN0LFxuICAgICAgICAgICAgICAgICAgICBzZXRJc3JTdGF0dXM6IHJvdXRlclNlcnZlckNvbnRleHQgPT0gbnVsbCA/IHZvaWQgMCA6IHJvdXRlclNlcnZlckNvbnRleHQuc2V0SXNyU3RhdHVzLFxuICAgICAgICAgICAgICAgICAgICBkaXI6IHJvdXRlTW9kdWxlLnByb2plY3REaXIsXG4gICAgICAgICAgICAgICAgICAgIGlzRHJhZnRNb2RlLFxuICAgICAgICAgICAgICAgICAgICBpc1JldmFsaWRhdGU6IGlzU1NHICYmICFwb3N0cG9uZWQgJiYgIWlzRHluYW1pY1JTQ1JlcXVlc3QsXG4gICAgICAgICAgICAgICAgICAgIGJvdFR5cGUsXG4gICAgICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgICAgICBpc1Bvc3NpYmxlU2VydmVyQWN0aW9uLFxuICAgICAgICAgICAgICAgICAgICBhc3NldFByZWZpeDogbmV4dENvbmZpZy5hc3NldFByZWZpeCxcbiAgICAgICAgICAgICAgICAgICAgbmV4dENvbmZpZ091dHB1dDogbmV4dENvbmZpZy5vdXRwdXQsXG4gICAgICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiBuZXh0Q29uZmlnLmNyb3NzT3JpZ2luLFxuICAgICAgICAgICAgICAgICAgICB0cmFpbGluZ1NsYXNoOiBuZXh0Q29uZmlnLnRyYWlsaW5nU2xhc2gsXG4gICAgICAgICAgICAgICAgICAgIHByZXZpZXdQcm9wczogcHJlcmVuZGVyTWFuaWZlc3QucHJldmlldyxcbiAgICAgICAgICAgICAgICAgICAgZGVwbG95bWVudElkOiBuZXh0Q29uZmlnLmRlcGxveW1lbnRJZCxcbiAgICAgICAgICAgICAgICAgICAgZW5hYmxlVGFpbnRpbmc6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLnRhaW50LFxuICAgICAgICAgICAgICAgICAgICBodG1sTGltaXRlZEJvdHM6IG5leHRDb25maWcuaHRtbExpbWl0ZWRCb3RzLFxuICAgICAgICAgICAgICAgICAgICBkZXZ0b29sU2VnbWVudEV4cGxvcmVyOiBuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5kZXZ0b29sU2VnbWVudEV4cGxvcmVyLFxuICAgICAgICAgICAgICAgICAgICByZWFjdE1heEhlYWRlcnNMZW5ndGg6IG5leHRDb25maWcucmVhY3RNYXhIZWFkZXJzTGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgICBtdWx0aVpvbmVEcmFmdE1vZGUsXG4gICAgICAgICAgICAgICAgICAgIGluY3JlbWVudGFsQ2FjaGU6IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2luY3JlbWVudGFsQ2FjaGUnKSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVMaWZlUHJvZmlsZXM6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLmNhY2hlTGlmZSxcbiAgICAgICAgICAgICAgICAgICAgYmFzZVBhdGg6IG5leHRDb25maWcuYmFzZVBhdGgsXG4gICAgICAgICAgICAgICAgICAgIHNlcnZlckFjdGlvbnM6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLnNlcnZlckFjdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIC4uLmlzRGVidWdTdGF0aWNTaGVsbCB8fCBpc0RlYnVnRHluYW1pY0FjY2Vzc2VzID8ge1xuICAgICAgICAgICAgICAgICAgICAgICAgbmV4dEV4cG9ydDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzU3RhdGljR2VuZXJhdGlvbjogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZTogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzRGVidWdEeW5hbWljQWNjZXNzZXM6IGlzRGVidWdEeW5hbWljQWNjZXNzZXNcbiAgICAgICAgICAgICAgICAgICAgfSA6IHt9LFxuICAgICAgICAgICAgICAgICAgICBleHBlcmltZW50YWw6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzUm91dGVQUFJFbmFibGVkLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlVGltZTogbmV4dENvbmZpZy5leHBpcmVUaW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhbGVUaW1lczogbmV4dENvbmZpZy5leHBlcmltZW50YWwuc3RhbGVUaW1lcyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGR5bmFtaWNJTzogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljSU8pLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xpZW50U2VnbWVudENhY2hlOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmNsaWVudFNlZ21lbnRDYWNoZSksXG4gICAgICAgICAgICAgICAgICAgICAgICBkeW5hbWljT25Ib3ZlcjogQm9vbGVhbihuZXh0Q29uZmlnLmV4cGVyaW1lbnRhbC5keW5hbWljT25Ib3ZlciksXG4gICAgICAgICAgICAgICAgICAgICAgICBpbmxpbmVDc3M6IEJvb2xlYW4obmV4dENvbmZpZy5leHBlcmltZW50YWwuaW5saW5lQ3NzKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGF1dGhJbnRlcnJ1cHRzOiBCb29sZWFuKG5leHRDb25maWcuZXhwZXJpbWVudGFsLmF1dGhJbnRlcnJ1cHRzKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsaWVudFRyYWNlTWV0YWRhdGE6IG5leHRDb25maWcuZXhwZXJpbWVudGFsLmNsaWVudFRyYWNlTWV0YWRhdGEgfHwgW11cbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgd2FpdFVudGlsOiBjdHgud2FpdFVudGlsLFxuICAgICAgICAgICAgICAgICAgICBvbkNsb3NlOiAoY2IpPT57XG4gICAgICAgICAgICAgICAgICAgICAgICByZXMub24oJ2Nsb3NlJywgY2IpO1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBvbkFmdGVyVGFza0Vycm9yOiAoKT0+e30sXG4gICAgICAgICAgICAgICAgICAgIG9uSW5zdHJ1bWVudGF0aW9uUmVxdWVzdEVycm9yOiAoZXJyb3IsIF9yZXF1ZXN0LCBlcnJvckNvbnRleHQpPT5yb3V0ZU1vZHVsZS5vblJlcXVlc3RFcnJvcihyZXEsIGVycm9yLCBlcnJvckNvbnRleHQsIHJvdXRlclNlcnZlckNvbnRleHQpLFxuICAgICAgICAgICAgICAgICAgICBlcnI6IGdldFJlcXVlc3RNZXRhKHJlcSwgJ2ludm9rZUVycm9yJyksXG4gICAgICAgICAgICAgICAgICAgIGRldjogcm91dGVNb2R1bGUuaXNEZXZcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgaW52b2tlUm91dGVNb2R1bGUoc3BhbiwgY29udGV4dCk7XG4gICAgICAgICAgICBjb25zdCB7IG1ldGFkYXRhIH0gPSByZXN1bHQ7XG4gICAgICAgICAgICBjb25zdCB7IGNhY2hlQ29udHJvbCwgaGVhZGVycyA9IHt9LCAvLyBBZGQgYW55IGZldGNoIHRhZ3MgdGhhdCB3ZXJlIG9uIHRoZSBwYWdlIHRvIHRoZSByZXNwb25zZSBoZWFkZXJzLlxuICAgICAgICAgICAgZmV0Y2hUYWdzOiBjYWNoZVRhZ3MgfSA9IG1ldGFkYXRhO1xuICAgICAgICAgICAgaWYgKGNhY2hlVGFncykge1xuICAgICAgICAgICAgICAgIGhlYWRlcnNbTkVYVF9DQUNIRV9UQUdTX0hFQURFUl0gPSBjYWNoZVRhZ3M7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBQdWxsIGFueSBmZXRjaCBtZXRyaWNzIGZyb20gdGhlIHJlbmRlciBvbnRvIHRoZSByZXF1ZXN0LlxuICAgICAgICAgICAgO1xuICAgICAgICAgICAgcmVxLmZldGNoTWV0cmljcyA9IG1ldGFkYXRhLmZldGNoTWV0cmljcztcbiAgICAgICAgICAgIC8vIHdlIGRvbid0IHRocm93IHN0YXRpYyB0byBkeW5hbWljIGVycm9ycyBpbiBkZXYgYXMgaXNTU0dcbiAgICAgICAgICAgIC8vIGlzIGEgYmVzdCBndWVzcyBpbiBkZXYgc2luY2Ugd2UgZG9uJ3QgaGF2ZSB0aGUgcHJlcmVuZGVyIHBhc3NcbiAgICAgICAgICAgIC8vIHRvIGtub3cgd2hldGhlciB0aGUgcGF0aCBpcyBhY3R1YWxseSBzdGF0aWMgb3Igbm90XG4gICAgICAgICAgICBpZiAoaXNTU0cgJiYgKGNhY2hlQ29udHJvbCA9PSBudWxsID8gdm9pZCAwIDogY2FjaGVDb250cm9sLnJldmFsaWRhdGUpID09PSAwICYmICFyb3V0ZU1vZHVsZS5pc0RldiAmJiAhaXNSb3V0ZVBQUkVuYWJsZWQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBzdGF0aWNCYWlsb3V0SW5mbyA9IG1ldGFkYXRhLnN0YXRpY0JhaWxvdXRJbmZvO1xuICAgICAgICAgICAgICAgIGNvbnN0IGVyciA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoYFBhZ2UgY2hhbmdlZCBmcm9tIHN0YXRpYyB0byBkeW5hbWljIGF0IHJ1bnRpbWUgJHtyZXNvbHZlZFBhdGhuYW1lfSR7KHN0YXRpY0JhaWxvdXRJbmZvID09IG51bGwgPyB2b2lkIDAgOiBzdGF0aWNCYWlsb3V0SW5mby5kZXNjcmlwdGlvbikgPyBgLCByZWFzb246ICR7c3RhdGljQmFpbG91dEluZm8uZGVzY3JpcHRpb259YCA6IGBgfWAgKyBgXFxuc2VlIG1vcmUgaGVyZSBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9hcHAtc3RhdGljLXRvLWR5bmFtaWMtZXJyb3JgKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUxMzJcIixcbiAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGlmIChzdGF0aWNCYWlsb3V0SW5mbyA9PSBudWxsID8gdm9pZCAwIDogc3RhdGljQmFpbG91dEluZm8uc3RhY2spIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhY2sgPSBzdGF0aWNCYWlsb3V0SW5mby5zdGFjaztcbiAgICAgICAgICAgICAgICAgICAgZXJyLnN0YWNrID0gZXJyLm1lc3NhZ2UgKyBzdGFjay5zdWJzdHJpbmcoc3RhY2suaW5kZXhPZignXFxuJykpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHZhbHVlOiB7XG4gICAgICAgICAgICAgICAgICAgIGtpbmQ6IENhY2hlZFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgICAgICAgICAgICAgaHRtbDogcmVzdWx0LFxuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICAgICAgICAgICAgICByc2NEYXRhOiBtZXRhZGF0YS5mbGlnaHREYXRhLFxuICAgICAgICAgICAgICAgICAgICBwb3N0cG9uZWQ6IG1ldGFkYXRhLnBvc3Rwb25lZCxcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBtZXRhZGF0YS5zdGF0dXNDb2RlLFxuICAgICAgICAgICAgICAgICAgICBzZWdtZW50RGF0YTogbWV0YWRhdGEuc2VnbWVudERhdGFcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2VHZW5lcmF0b3IgPSBhc3luYyAoeyBoYXNSZXNvbHZlZCwgcHJldmlvdXNDYWNoZUVudHJ5LCBpc1JldmFsaWRhdGluZywgc3BhbiB9KT0+e1xuICAgICAgICAgICAgY29uc3QgaXNQcm9kdWN0aW9uID0gcm91dGVNb2R1bGUuaXNEZXYgPT09IGZhbHNlO1xuICAgICAgICAgICAgY29uc3QgZGlkUmVzcG9uZCA9IGhhc1Jlc29sdmVkIHx8IHJlcy53cml0YWJsZUVuZGVkO1xuICAgICAgICAgICAgLy8gc2tpcCBvbi1kZW1hbmQgcmV2YWxpZGF0ZSBpZiBjYWNoZSBpcyBub3QgcHJlc2VudCBhbmRcbiAgICAgICAgICAgIC8vIHJldmFsaWRhdGUtaWYtZ2VuZXJhdGVkIGlzIHNldFxuICAgICAgICAgICAgaWYgKGlzT25EZW1hbmRSZXZhbGlkYXRlICYmIHJldmFsaWRhdGVPbmx5R2VuZXJhdGVkICYmICFwcmV2aW91c0NhY2hlRW50cnkgJiYgIW1pbmltYWxNb2RlKSB7XG4gICAgICAgICAgICAgICAgaWYgKHJvdXRlclNlcnZlckNvbnRleHQgPT0gbnVsbCA/IHZvaWQgMCA6IHJvdXRlclNlcnZlckNvbnRleHQucmVuZGVyNDA0KSB7XG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IHJvdXRlclNlcnZlckNvbnRleHQucmVuZGVyNDA0KHJlcSwgcmVzKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDQwNDtcbiAgICAgICAgICAgICAgICAgICAgcmVzLmVuZCgnVGhpcyBwYWdlIGNvdWxkIG5vdCBiZSBmb3VuZCcpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBmYWxsYmFja01vZGU7XG4gICAgICAgICAgICBpZiAocHJlcmVuZGVySW5mbykge1xuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9IHBhcnNlRmFsbGJhY2tGaWVsZChwcmVyZW5kZXJJbmZvLmZhbGxiYWNrKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFdoZW4gc2VydmluZyBhIGJvdCByZXF1ZXN0LCB3ZSB3YW50IHRvIHNlcnZlIGEgYmxvY2tpbmcgcmVuZGVyIGFuZCBub3RcbiAgICAgICAgICAgIC8vIHRoZSBwcmVyZW5kZXJlZCBwYWdlLiBUaGlzIGVuc3VyZXMgdGhhdCB0aGUgY29ycmVjdCBjb250ZW50IGlzIHNlcnZlZFxuICAgICAgICAgICAgLy8gdG8gdGhlIGJvdCBpbiB0aGUgaGVhZC5cbiAgICAgICAgICAgIGlmIChmYWxsYmFja01vZGUgPT09IEZhbGxiYWNrTW9kZS5QUkVSRU5ERVIgJiYgaXNCb3QodXNlckFnZW50KSkge1xuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9IEZhbGxiYWNrTW9kZS5CTE9DS0lOR19TVEFUSUNfUkVOREVSO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKChwcmV2aW91c0NhY2hlRW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZpb3VzQ2FjaGVFbnRyeS5pc1N0YWxlKSA9PT0gLTEpIHtcbiAgICAgICAgICAgICAgICBpc09uRGVtYW5kUmV2YWxpZGF0ZSA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBUT0RPOiBhZGFwdCBmb3IgUFBSXG4gICAgICAgICAgICAvLyBvbmx5IGFsbG93IG9uLWRlbWFuZCByZXZhbGlkYXRlIGZvciBmYWxsYmFjazogdHJ1ZS9ibG9ja2luZ1xuICAgICAgICAgICAgLy8gb3IgZm9yIHByZXJlbmRlcmVkIGZhbGxiYWNrOiBmYWxzZSBwYXRoc1xuICAgICAgICAgICAgaWYgKGlzT25EZW1hbmRSZXZhbGlkYXRlICYmIChmYWxsYmFja01vZGUgIT09IEZhbGxiYWNrTW9kZS5OT1RfRk9VTkQgfHwgcHJldmlvdXNDYWNoZUVudHJ5KSkge1xuICAgICAgICAgICAgICAgIGZhbGxiYWNrTW9kZSA9IEZhbGxiYWNrTW9kZS5CTE9DS0lOR19TVEFUSUNfUkVOREVSO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFtaW5pbWFsTW9kZSAmJiBmYWxsYmFja01vZGUgIT09IEZhbGxiYWNrTW9kZS5CTE9DS0lOR19TVEFUSUNfUkVOREVSICYmIHN0YXRpY1BhdGhLZXkgJiYgIWRpZFJlc3BvbmQgJiYgIWlzRHJhZnRNb2RlICYmIHBhZ2VJc0R5bmFtaWMgJiYgKGlzUHJvZHVjdGlvbiB8fCAhaXNQcmVyZW5kZXJlZCkpIHtcbiAgICAgICAgICAgICAgICAvLyBpZiB0aGUgcGFnZSBoYXMgZHluYW1pY1BhcmFtczogZmFsc2UgYW5kIHRoaXMgcGF0aG5hbWUgd2Fzbid0XG4gICAgICAgICAgICAgICAgLy8gcHJlcmVuZGVyZWQgdHJpZ2dlciB0aGUgbm8gZmFsbGJhY2sgaGFuZGxpbmdcbiAgICAgICAgICAgICAgICBpZiAoLy8gSW4gZGV2ZWxvcG1lbnQsIGZhbGwgdGhyb3VnaCB0byByZW5kZXIgdG8gaGFuZGxlIG1pc3NpbmdcbiAgICAgICAgICAgICAgICAvLyBnZXRTdGF0aWNQYXRocy5cbiAgICAgICAgICAgICAgICAoaXNQcm9kdWN0aW9uIHx8IHByZXJlbmRlckluZm8pICYmIC8vIFdoZW4gZmFsbGJhY2sgaXNuJ3QgcHJlc2VudCwgYWJvcnQgdGhpcyByZW5kZXIgc28gd2UgNDA0XG4gICAgICAgICAgICAgICAgZmFsbGJhY2tNb2RlID09PSBGYWxsYmFja01vZGUuTk9UX0ZPVU5EKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBOb0ZhbGxiYWNrRXJyb3IoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgbGV0IGZhbGxiYWNrUmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgaWYgKGlzUm91dGVQUFJFbmFibGVkICYmICFpc1JTQ1JlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gV2UgdXNlIHRoZSByZXNwb25zZSBjYWNoZSBoZXJlIHRvIGhhbmRsZSB0aGUgcmV2YWxpZGF0aW9uIGFuZFxuICAgICAgICAgICAgICAgICAgICAvLyBtYW5hZ2VtZW50IG9mIHRoZSBmYWxsYmFjayBzaGVsbC5cbiAgICAgICAgICAgICAgICAgICAgZmFsbGJhY2tSZXNwb25zZSA9IGF3YWl0IHJvdXRlTW9kdWxlLmhhbmRsZVJlc3BvbnNlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlS2V5OiBpc1Byb2R1Y3Rpb24gPyBub3JtYWxpemVkU3JjUGFnZSA6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0Q29uZmlnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVLaW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0ZhbGxiYWNrOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgcHJlcmVuZGVyTWFuaWZlc3QsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1JvdXRlUFBSRW5hYmxlZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlR2VuZXJhdG9yOiBhc3luYyAoKT0+ZG9SZW5kZXIoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBXZSBwYXNzIGB1bmRlZmluZWRgIGFzIHJlbmRlcmluZyBhIGZhbGxiYWNrIGlzbid0IHJlc3VtZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gaGVyZS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zdHBvbmVkOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbGxiYWNrUm91dGVQYXJhbXM6IC8vIElmIHdlJ3JlIGluIHByb2R1Y3Rpb24gb3Igd2UncmUgZGVidWdnaW5nIHRoZSBmYWxsYmFja1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBzaGVsbCB0aGVuIHdlIHNob3VsZCBwb3N0cG9uZSB3aGVuIGR5bmFtaWMgcGFyYW1zIGFyZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBhY2Nlc3NlZC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNQcm9kdWN0aW9uIHx8IGlzRGVidWdGYWxsYmFja1NoZWxsID8gZ2V0RmFsbGJhY2tSb3V0ZVBhcmFtcyhub3JtYWxpemVkU3JjUGFnZSkgOiBudWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICAgICAgICB3YWl0VW50aWw6IGN0eC53YWl0VW50aWxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSBmYWxsYmFjayByZXNwb25zZSB3YXMgc2V0IHRvIG51bGwsIHRoZW4gd2Ugc2hvdWxkIHJldHVybiBudWxsLlxuICAgICAgICAgICAgICAgICAgICBpZiAoZmFsbGJhY2tSZXNwb25zZSA9PT0gbnVsbCkgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgICAgIC8vIE90aGVyd2lzZSwgaWYgd2UgZGlkIGdldCBhIGZhbGxiYWNrIHJlc3BvbnNlLCB3ZSBzaG91bGQgcmV0dXJuIGl0LlxuICAgICAgICAgICAgICAgICAgICBpZiAoZmFsbGJhY2tSZXNwb25zZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUmVtb3ZlIHRoZSBjYWNoZSBjb250cm9sIGZyb20gdGhlIHJlc3BvbnNlIHRvIHByZXZlbnQgaXQgZnJvbSBiZWluZ1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gdXNlZCBpbiB0aGUgc3Vycm91bmRpbmcgY2FjaGUuXG4gICAgICAgICAgICAgICAgICAgICAgICBkZWxldGUgZmFsbGJhY2tSZXNwb25zZS5jYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsbGJhY2tSZXNwb25zZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIE9ubHkgcmVxdWVzdHMgdGhhdCBhcmVuJ3QgcmV2YWxpZGF0aW5nIGNhbiBiZSByZXN1bWVkLiBJZiB3ZSBoYXZlIHRoZVxuICAgICAgICAgICAgLy8gbWluaW1hbCBwb3N0cG9uZWQgZGF0YSwgdGhlbiB3ZSBzaG91bGQgcmVzdW1lIHRoZSByZW5kZXIgd2l0aCBpdC5cbiAgICAgICAgICAgIGNvbnN0IHBvc3Rwb25lZCA9ICFpc09uRGVtYW5kUmV2YWxpZGF0ZSAmJiAhaXNSZXZhbGlkYXRpbmcgJiYgbWluaW1hbFBvc3Rwb25lZCA/IG1pbmltYWxQb3N0cG9uZWQgOiB1bmRlZmluZWQ7XG4gICAgICAgICAgICAvLyBXaGVuIHdlJ3JlIGluIG1pbmltYWwgbW9kZSwgaWYgd2UncmUgdHJ5aW5nIHRvIGRlYnVnIHRoZSBzdGF0aWMgc2hlbGwsXG4gICAgICAgICAgICAvLyB3ZSBzaG91bGQganVzdCByZXR1cm4gbm90aGluZyBpbnN0ZWFkIG9mIHJlc3VtaW5nIHRoZSBkeW5hbWljIHJlbmRlci5cbiAgICAgICAgICAgIGlmICgoaXNEZWJ1Z1N0YXRpY1NoZWxsIHx8IGlzRGVidWdEeW5hbWljQWNjZXNzZXMpICYmIHR5cGVvZiBwb3N0cG9uZWQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAxLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGtpbmQ6IENhY2hlZFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGh0bWw6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKCcnKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2VEYXRhOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIGR5bmFtaWMgcm91dGUgd2l0aCBQUFIgZW5hYmxlZCBhbmQgdGhlIGRlZmF1bHQgcm91dGVcbiAgICAgICAgICAgIC8vIG1hdGNoZXMgd2VyZSBzZXQsIHRoZW4gd2Ugc2hvdWxkIHBhc3MgdGhlIGZhbGxiYWNrIHJvdXRlIHBhcmFtcyB0b1xuICAgICAgICAgICAgLy8gdGhlIHJlbmRlcmVyIGFzIHRoaXMgaXMgYSBmYWxsYmFjayByZXZhbGlkYXRpb24gcmVxdWVzdC5cbiAgICAgICAgICAgIGNvbnN0IGZhbGxiYWNrUm91dGVQYXJhbXMgPSBwYWdlSXNEeW5hbWljICYmIGlzUm91dGVQUFJFbmFibGVkICYmIChnZXRSZXF1ZXN0TWV0YShyZXEsICdyZW5kZXJGYWxsYmFja1NoZWxsJykgfHwgaXNEZWJ1Z0ZhbGxiYWNrU2hlbGwpID8gZ2V0RmFsbGJhY2tSb3V0ZVBhcmFtcyhwYXRobmFtZSkgOiBudWxsO1xuICAgICAgICAgICAgLy8gUGVyZm9ybSB0aGUgcmVuZGVyLlxuICAgICAgICAgICAgcmV0dXJuIGRvUmVuZGVyKHtcbiAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgIHBvc3Rwb25lZCxcbiAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaGFuZGxlUmVzcG9uc2UgPSBhc3luYyAoc3Bhbik9PntcbiAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV92YWx1ZSwgX2NhY2hlZERhdGFfaGVhZGVycztcbiAgICAgICAgICAgIGNvbnN0IGNhY2hlRW50cnkgPSBhd2FpdCByb3V0ZU1vZHVsZS5oYW5kbGVSZXNwb25zZSh7XG4gICAgICAgICAgICAgICAgY2FjaGVLZXk6IHNzZ0NhY2hlS2V5LFxuICAgICAgICAgICAgICAgIHJlc3BvbnNlR2VuZXJhdG9yOiAoYyk9PnJlc3BvbnNlR2VuZXJhdG9yKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4sXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5jXG4gICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIHJvdXRlS2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlLFxuICAgICAgICAgICAgICAgIGlzUm91dGVQUFJFbmFibGVkLFxuICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICBuZXh0Q29uZmlnLFxuICAgICAgICAgICAgICAgIHByZXJlbmRlck1hbmlmZXN0LFxuICAgICAgICAgICAgICAgIHdhaXRVbnRpbDogY3R4LndhaXRVbnRpbFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoaXNEcmFmdE1vZGUpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCdDYWNoZS1Db250cm9sJywgJ3ByaXZhdGUsIG5vLWNhY2hlLCBuby1zdG9yZSwgbWF4LWFnZT0wLCBtdXN0LXJldmFsaWRhdGUnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEluIGRldiwgd2Ugc2hvdWxkIG5vdCBjYWNoZSBwYWdlcyBmb3IgYW55IHJlYXNvbi5cbiAgICAgICAgICAgIGlmIChyb3V0ZU1vZHVsZS5pc0Rldikge1xuICAgICAgICAgICAgICAgIHJlcy5zZXRIZWFkZXIoJ0NhY2hlLUNvbnRyb2wnLCAnbm8tc3RvcmUsIG11c3QtcmV2YWxpZGF0ZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFjYWNoZUVudHJ5KSB7XG4gICAgICAgICAgICAgICAgaWYgKHNzZ0NhY2hlS2V5KSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEEgY2FjaGUgZW50cnkgbWlnaHQgbm90IGJlIGdlbmVyYXRlZCBpZiBhIHJlc3BvbnNlIGlzIHdyaXR0ZW5cbiAgICAgICAgICAgICAgICAgICAgLy8gaW4gYGdldEluaXRpYWxQcm9wc2Agb3IgYGdldFNlcnZlclNpZGVQcm9wc2AsIGJ1dCB0aG9zZSBzaG91bGRuJ3RcbiAgICAgICAgICAgICAgICAgICAgLy8gaGF2ZSBhIGNhY2hlIGtleS4gSWYgd2UgZG8gaGF2ZSBhIGNhY2hlIGtleSBidXQgd2UgZG9uJ3QgZW5kIHVwXG4gICAgICAgICAgICAgICAgICAgIC8vIHdpdGggYSBjYWNoZSBlbnRyeSwgdGhlbiBlaXRoZXIgTmV4dC5qcyBvciB0aGUgYXBwbGljYXRpb24gaGFzIGFcbiAgICAgICAgICAgICAgICAgICAgLy8gYnVnIHRoYXQgbmVlZHMgZml4aW5nLlxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdpbnZhcmlhbnQ6IGNhY2hlIGVudHJ5IHJlcXVpcmVkIGJ1dCBub3QgZ2VuZXJhdGVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTYyXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKChfY2FjaGVFbnRyeV92YWx1ZSA9IGNhY2hlRW50cnkudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV92YWx1ZS5raW5kKSAhPT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9QQUdFKSB7XG4gICAgICAgICAgICAgICAgdmFyIF9jYWNoZUVudHJ5X3ZhbHVlMTtcbiAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhcmlhbnQgYXBwLXBhZ2UgaGFuZGxlciByZWNlaXZlZCBpbnZhbGlkIGNhY2hlIGVudHJ5ICR7KF9jYWNoZUVudHJ5X3ZhbHVlMSA9IGNhY2hlRW50cnkudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV92YWx1ZTEua2luZH1gKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkU3MDdcIixcbiAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZGlkUG9zdHBvbmUgPSB0eXBlb2YgY2FjaGVFbnRyeS52YWx1ZS5wb3N0cG9uZWQgPT09ICdzdHJpbmcnO1xuICAgICAgICAgICAgaWYgKGlzU1NHICYmIC8vIFdlIGRvbid0IHdhbnQgdG8gc2VuZCBhIGNhY2hlIGhlYWRlciBmb3IgcmVxdWVzdHMgdGhhdCBjb250YWluIGR5bmFtaWNcbiAgICAgICAgICAgIC8vIGRhdGEuIElmIHRoaXMgaXMgYSBEeW5hbWljIFJTQyByZXF1ZXN0IG9yIHdhc24ndCBhIFByZWZldGNoIFJTQ1xuICAgICAgICAgICAgLy8gcmVxdWVzdCwgdGhlbiB3ZSBzaG91bGQgc2V0IHRoZSBjYWNoZSBoZWFkZXIuXG4gICAgICAgICAgICAhaXNEeW5hbWljUlNDUmVxdWVzdCAmJiAoIWRpZFBvc3Rwb25lIHx8IGlzUHJlZmV0Y2hSU0NSZXF1ZXN0KSkge1xuICAgICAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gc2V0IHgtbmV4dGpzLWNhY2hlIGhlYWRlciB0byBtYXRjaCB0aGUgaGVhZGVyXG4gICAgICAgICAgICAgICAgICAgIC8vIHdlIHNldCBmb3IgdGhlIGltYWdlLW9wdGltaXplclxuICAgICAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKCd4LW5leHRqcy1jYWNoZScsIGlzT25EZW1hbmRSZXZhbGlkYXRlID8gJ1JFVkFMSURBVEVEJyA6IGNhY2hlRW50cnkuaXNNaXNzID8gJ01JU1MnIDogY2FjaGVFbnRyeS5pc1N0YWxlID8gJ1NUQUxFJyA6ICdISVQnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gU2V0IGEgaGVhZGVyIHVzZWQgYnkgdGhlIGNsaWVudCByb3V0ZXIgdG8gc2lnbmFsIHRoZSByZXNwb25zZSBpcyBzdGF0aWNcbiAgICAgICAgICAgICAgICAvLyBhbmQgc2hvdWxkIHJlc3BlY3QgdGhlIGBzdGF0aWNgIGNhY2hlIHN0YWxlVGltZSB2YWx1ZS5cbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKE5FWFRfSVNfUFJFUkVOREVSX0hFQURFUiwgJzEnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHsgdmFsdWU6IGNhY2hlZERhdGEgfSA9IGNhY2hlRW50cnk7XG4gICAgICAgICAgICAvLyBDb2VyY2UgdGhlIGNhY2hlIGNvbnRyb2wgcGFyYW1ldGVyIGZyb20gdGhlIHJlbmRlci5cbiAgICAgICAgICAgIGxldCBjYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICAvLyBJZiB0aGlzIGlzIGEgcmVzdW1lIHJlcXVlc3QgaW4gbWluaW1hbCBtb2RlIGl0IGlzIHN0cmVhbWVkIHdpdGggZHluYW1pY1xuICAgICAgICAgICAgLy8gY29udGVudCBhbmQgc2hvdWxkIG5vdCBiZSBjYWNoZWQuXG4gICAgICAgICAgICBpZiAobWluaW1hbFBvc3Rwb25lZCkge1xuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbCA9IHtcbiAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSBlbHNlIGlmIChtaW5pbWFsTW9kZSAmJiBpc1JTQ1JlcXVlc3QgJiYgIWlzUHJlZmV0Y2hSU0NSZXF1ZXN0ICYmIGlzUm91dGVQUFJFbmFibGVkKSB7XG4gICAgICAgICAgICAgICAgY2FjaGVDb250cm9sID0ge1xuICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9IGVsc2UgaWYgKCFyb3V0ZU1vZHVsZS5pc0Rldikge1xuICAgICAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSBwcmV2aWV3IG1vZGUgcmVxdWVzdCwgd2Ugc2hvdWxkbid0IGNhY2hlIGl0XG4gICAgICAgICAgICAgICAgaWYgKGlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldmFsaWRhdGU6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoIWlzU1NHKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghcmVzLmdldEhlYWRlcignQ2FjaGUtQ29udHJvbCcpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gSWYgdGhlIGNhY2hlIGVudHJ5IGhhcyBhIGNhY2hlIGNvbnRyb2wgd2l0aCBhIHJldmFsaWRhdGUgdmFsdWUgdGhhdCdzXG4gICAgICAgICAgICAgICAgICAgIC8vIGEgbnVtYmVyLCB1c2UgaXQuXG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBfY2FjaGVFbnRyeV9jYWNoZUNvbnRyb2w7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZSA8IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhbGlkIHJldmFsaWRhdGUgY29uZmlndXJhdGlvbiBwcm92aWRlZDogJHtjYWNoZUVudHJ5LmNhY2hlQ29udHJvbC5yZXZhbGlkYXRlfSA8IDFgKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIkUyMlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wucmV2YWxpZGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6ICgoX2NhY2hlRW50cnlfY2FjaGVDb250cm9sID0gY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2wpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVFbnRyeV9jYWNoZUNvbnRyb2wuZXhwaXJlKSA/PyBuZXh0Q29uZmlnLmV4cGlyZVRpbWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2wgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogQ0FDSEVfT05FX1lFQVIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYWNoZUVudHJ5LmNhY2hlQ29udHJvbCA9IGNhY2hlQ29udHJvbDtcbiAgICAgICAgICAgIGlmICh0eXBlb2Ygc2VnbWVudFByZWZldGNoSGVhZGVyID09PSAnc3RyaW5nJyAmJiAoY2FjaGVkRGF0YSA9PSBudWxsID8gdm9pZCAwIDogY2FjaGVkRGF0YS5raW5kKSA9PT0gQ2FjaGVkUm91dGVLaW5kLkFQUF9QQUdFICYmIGNhY2hlZERhdGEuc2VnbWVudERhdGEpIHtcbiAgICAgICAgICAgICAgICB2YXIgX2NhY2hlZERhdGFfaGVhZGVyczE7XG4gICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBhIHByZWZldGNoIHJlcXVlc3QgaXNzdWVkIGJ5IHRoZSBjbGllbnQgU2VnbWVudCBDYWNoZS4gVGhlc2VcbiAgICAgICAgICAgICAgICAvLyBzaG91bGQgbmV2ZXIgcmVhY2ggdGhlIGFwcGxpY2F0aW9uIGxheWVyIChsYW1iZGEpLiBXZSBzaG91bGQgZWl0aGVyXG4gICAgICAgICAgICAgICAgLy8gcmVzcG9uZCBmcm9tIHRoZSBjYWNoZSAoSElUKSBvciByZXNwb25kIHdpdGggMjA0IE5vIENvbnRlbnQgKE1JU1MpLlxuICAgICAgICAgICAgICAgIC8vIFNldCBhIGhlYWRlciB0byBpbmRpY2F0ZSB0aGF0IFBQUiBpcyBlbmFibGVkIGZvciB0aGlzIHJvdXRlLiBUaGlzXG4gICAgICAgICAgICAgICAgLy8gbGV0cyB0aGUgY2xpZW50IGRpc3Rpbmd1aXNoIGJldHdlZW4gYSByZWd1bGFyIGNhY2hlIG1pc3MgYW5kIGEgY2FjaGVcbiAgICAgICAgICAgICAgICAvLyBtaXNzIGR1ZSB0byBQUFIgYmVpbmcgZGlzYWJsZWQuIEluIG90aGVyIGNvbnRleHRzIHRoaXMgaGVhZGVyIGlzIHVzZWRcbiAgICAgICAgICAgICAgICAvLyB0byBpbmRpY2F0ZSB0aGF0IHRoZSByZXNwb25zZSBjb250YWlucyBkeW5hbWljIGRhdGEsIGJ1dCBoZXJlIHdlJ3JlXG4gICAgICAgICAgICAgICAgLy8gb25seSB1c2luZyBpdCB0byBpbmRpY2F0ZSB0aGF0IHRoZSBmZWF0dXJlIGlzIGVuYWJsZWQg4oCUIHRoZSBzZWdtZW50XG4gICAgICAgICAgICAgICAgLy8gcmVzcG9uc2UgaXRzZWxmIGNvbnRhaW5zIHdoZXRoZXIgdGhlIGRhdGEgaXMgZHluYW1pYy5cbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKE5FWFRfRElEX1BPU1RQT05FX0hFQURFUiwgJzInKTtcbiAgICAgICAgICAgICAgICAvLyBBZGQgdGhlIGNhY2hlIHRhZ3MgaGVhZGVyIHRvIHRoZSByZXNwb25zZSBpZiBpdCBleGlzdHMgYW5kIHdlJ3JlIGluXG4gICAgICAgICAgICAgICAgLy8gbWluaW1hbCBtb2RlIHdoaWxlIHJlbmRlcmluZyBhIHN0YXRpYyBwYWdlLlxuICAgICAgICAgICAgICAgIGNvbnN0IHRhZ3MgPSAoX2NhY2hlZERhdGFfaGVhZGVyczEgPSBjYWNoZWREYXRhLmhlYWRlcnMpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVkRGF0YV9oZWFkZXJzMVtORVhUX0NBQ0hFX1RBR1NfSEVBREVSXTtcbiAgICAgICAgICAgICAgICBpZiAobWluaW1hbE1vZGUgJiYgaXNTU0cgJiYgdGFncyAmJiB0eXBlb2YgdGFncyA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0NBQ0hFX1RBR1NfSEVBREVSLCB0YWdzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgbWF0Y2hlZFNlZ21lbnQgPSBjYWNoZWREYXRhLnNlZ21lbnREYXRhLmdldChzZWdtZW50UHJlZmV0Y2hIZWFkZXIpO1xuICAgICAgICAgICAgICAgIGlmIChtYXRjaGVkU2VnbWVudCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIENhY2hlIGhpdFxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAncnNjJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGdlbmVyYXRlRXRhZ3M6IG5leHRDb25maWcuZ2VuZXJhdGVFdGFncyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvd2VyZWRCeUhlYWRlcjogbmV4dENvbmZpZy5wb3dlcmVkQnlIZWFkZXIsXG4gICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKG1hdGNoZWRTZWdtZW50KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIENhY2hlIG1pc3MuIEVpdGhlciBhIGNhY2hlIGVudHJ5IGZvciB0aGlzIHJvdXRlIGhhcyBub3QgYmVlbiBnZW5lcmF0ZWRcbiAgICAgICAgICAgICAgICAvLyAod2hpY2ggdGVjaG5pY2FsbHkgc2hvdWxkIG5vdCBiZSBwb3NzaWJsZSB3aGVuIFBQUiBpcyBlbmFibGVkLCBiZWNhdXNlXG4gICAgICAgICAgICAgICAgLy8gYXQgYSBtaW5pbXVtIHRoZXJlIHNob3VsZCBhbHdheXMgYmUgYSBmYWxsYmFjayBlbnRyeSkgb3IgdGhlcmUncyBub1xuICAgICAgICAgICAgICAgIC8vIG1hdGNoIGZvciB0aGUgcmVxdWVzdGVkIHNlZ21lbnQuIFJlc3BvbmQgd2l0aCBhIDIwNCBObyBDb250ZW50LiBXZVxuICAgICAgICAgICAgICAgIC8vIGRvbid0IGJvdGhlciB0byByZXNwb25kIHdpdGggNDA0LCBiZWNhdXNlIHRoZXNlIHJlcXVlc3RzIGFyZSBvbmx5XG4gICAgICAgICAgICAgICAgLy8gaXNzdWVkIGFzIHBhcnQgb2YgYSBwcmVmZXRjaC5cbiAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDIwNDtcbiAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAncnNjJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKCcnKSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiBjYWNoZUVudHJ5LmNhY2hlQ29udHJvbFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhlcmUncyBhIGNhbGxiYWNrIGZvciBgb25DYWNoZUVudHJ5YCwgY2FsbCBpdCB3aXRoIHRoZSBjYWNoZSBlbnRyeVxuICAgICAgICAgICAgLy8gYW5kIHRoZSByZXZhbGlkYXRlIG9wdGlvbnMuXG4gICAgICAgICAgICBjb25zdCBvbkNhY2hlRW50cnkgPSBnZXRSZXF1ZXN0TWV0YShyZXEsICdvbkNhY2hlRW50cnknKTtcbiAgICAgICAgICAgIGlmIChvbkNhY2hlRW50cnkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaW5pc2hlZCA9IGF3YWl0IG9uQ2FjaGVFbnRyeSh7XG4gICAgICAgICAgICAgICAgICAgIC4uLmNhY2hlRW50cnksXG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IHJlbW92ZSB0aGlzIHdoZW4gdXBzdHJlYW0gZG9lc24ndFxuICAgICAgICAgICAgICAgICAgICAvLyBhbHdheXMgZXhwZWN0IHRoaXMgdmFsdWUgdG8gYmUgXCJQQUdFXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmNhY2hlRW50cnkudmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICBraW5kOiAnUEFHRSdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICAgICAgdXJsOiBnZXRSZXF1ZXN0TWV0YShyZXEsICdpbml0VVJMJylcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoZmluaXNoZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzogbWF5YmUgd2UgaGF2ZSB0byBlbmQgdGhlIHJlcXVlc3Q/XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZSByZXF1ZXN0IGhhcyBhIHBvc3Rwb25lZCBzdGF0ZSBhbmQgaXQncyBhIHJlc3VtZSByZXF1ZXN0IHdlXG4gICAgICAgICAgICAvLyBzaG91bGQgZXJyb3IuXG4gICAgICAgICAgICBpZiAoZGlkUG9zdHBvbmUgJiYgbWluaW1hbFBvc3Rwb25lZCkge1xuICAgICAgICAgICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoJ0ludmFyaWFudDogcG9zdHBvbmVkIHN0YXRlIHNob3VsZCBub3QgYmUgcHJlc2VudCBvbiBhIHJlc3VtZSByZXF1ZXN0JyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMzk2XCIsXG4gICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChjYWNoZWREYXRhLmhlYWRlcnMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBoZWFkZXJzID0ge1xuICAgICAgICAgICAgICAgICAgICAuLi5jYWNoZWREYXRhLmhlYWRlcnNcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUgfHwgIWlzU1NHKSB7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBmb3IgKGxldCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoaGVhZGVycykpe1xuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAndW5kZWZpbmVkJykgY29udGludWU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCB2IG9mIHZhbHVlKXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXMuYXBwZW5kSGVhZGVyKGtleSwgdik7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSB2YWx1ZS50b1N0cmluZygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLmFwcGVuZEhlYWRlcihrZXksIHZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcy5hcHBlbmRIZWFkZXIoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBBZGQgdGhlIGNhY2hlIHRhZ3MgaGVhZGVyIHRvIHRoZSByZXNwb25zZSBpZiBpdCBleGlzdHMgYW5kIHdlJ3JlIGluXG4gICAgICAgICAgICAvLyBtaW5pbWFsIG1vZGUgd2hpbGUgcmVuZGVyaW5nIGEgc3RhdGljIHBhZ2UuXG4gICAgICAgICAgICBjb25zdCB0YWdzID0gKF9jYWNoZWREYXRhX2hlYWRlcnMgPSBjYWNoZWREYXRhLmhlYWRlcnMpID09IG51bGwgPyB2b2lkIDAgOiBfY2FjaGVkRGF0YV9oZWFkZXJzW05FWFRfQ0FDSEVfVEFHU19IRUFERVJdO1xuICAgICAgICAgICAgaWYgKG1pbmltYWxNb2RlICYmIGlzU1NHICYmIHRhZ3MgJiYgdHlwZW9mIHRhZ3MgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgcmVzLnNldEhlYWRlcihORVhUX0NBQ0hFX1RBR1NfSEVBREVSLCB0YWdzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHRoZSByZXF1ZXN0IGlzIGEgZGF0YSByZXF1ZXN0LCB0aGVuIHdlIHNob3VsZG4ndCBzZXQgdGhlIHN0YXR1cyBjb2RlXG4gICAgICAgICAgICAvLyBmcm9tIHRoZSByZXNwb25zZSBiZWNhdXNlIGl0IHNob3VsZCBhbHdheXMgYmUgMjAwLiBUaGlzIHNob3VsZCBiZSBnYXRlZFxuICAgICAgICAgICAgLy8gYmVoaW5kIHRoZSBleHBlcmltZW50YWwgUFBSIGZsYWcuXG4gICAgICAgICAgICBpZiAoY2FjaGVkRGF0YS5zdGF0dXMgJiYgKCFpc1JTQ1JlcXVlc3QgfHwgIWlzUm91dGVQUFJFbmFibGVkKSkge1xuICAgICAgICAgICAgICAgIHJlcy5zdGF0dXNDb2RlID0gY2FjaGVkRGF0YS5zdGF0dXM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBSZWRpcmVjdCBpbmZvcm1hdGlvbiBpcyBlbmNvZGVkIGluIFJTQyBwYXlsb2FkLCBzbyB3ZSBkb24ndCBuZWVkIHRvIHVzZSByZWRpcmVjdCBzdGF0dXMgY29kZXNcbiAgICAgICAgICAgIGlmICghbWluaW1hbE1vZGUgJiYgY2FjaGVkRGF0YS5zdGF0dXMgJiYgUmVkaXJlY3RTdGF0dXNDb2RlW2NhY2hlZERhdGEuc3RhdHVzXSAmJiBpc1JTQ1JlcXVlc3QpIHtcbiAgICAgICAgICAgICAgICByZXMuc3RhdHVzQ29kZSA9IDIwMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIE1hcmsgdGhhdCB0aGUgcmVxdWVzdCBkaWQgcG9zdHBvbmUuXG4gICAgICAgICAgICBpZiAoZGlkUG9zdHBvbmUpIHtcbiAgICAgICAgICAgICAgICByZXMuc2V0SGVhZGVyKE5FWFRfRElEX1BPU1RQT05FX0hFQURFUiwgJzEnKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIHdlIGRvbid0IGdvIHRocm91Z2ggdGhpcyBibG9jayB3aGVuIHByZXZpZXcgbW9kZSBpcyB0cnVlXG4gICAgICAgICAgICAvLyBhcyBwcmV2aWV3IG1vZGUgaXMgYSBkeW5hbWljIHJlcXVlc3QgKGJ5cGFzc2VzIGNhY2hlKSBhbmQgZG9lc24ndFxuICAgICAgICAgICAgLy8gZ2VuZXJhdGUgYm90aCBIVE1MIGFuZCBwYXlsb2FkcyBpbiB0aGUgc2FtZSByZXF1ZXN0IHNvIGNvbnRpbnVlIHRvIGp1c3RcbiAgICAgICAgICAgIC8vIHJldHVybiB0aGUgZ2VuZXJhdGVkIHBheWxvYWRcbiAgICAgICAgICAgIGlmIChpc1JTQ1JlcXVlc3QgJiYgIWlzRHJhZnRNb2RlKSB7XG4gICAgICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIGR5bmFtaWMgUlNDIHJlcXVlc3QsIHRoZW4gc3RyZWFtIHRoZSByZXNwb25zZS5cbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGNhY2hlZERhdGEucnNjRGF0YSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNhY2hlZERhdGEucG9zdHBvbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IEV4cGVjdGVkIHBvc3Rwb25lZCB0byBiZSB1bmRlZmluZWQnKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTM3MlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3JzYycsXG4gICAgICAgICAgICAgICAgICAgICAgICBnZW5lcmF0ZUV0YWdzOiBuZXh0Q29uZmlnLmdlbmVyYXRlRXRhZ3MsXG4gICAgICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBjYWNoZWREYXRhLmh0bWwsXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBEeW5hbWljIFJTQyByZXNwb25zZXMgY2Fubm90IGJlIGNhY2hlZCwgZXZlbiBpZiB0aGV5J3JlXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBjb25maWd1cmVkIHdpdGggYGZvcmNlLXN0YXRpY2AgYmVjYXVzZSB3ZSBoYXZlIG5vIHdheSBvZlxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gZGlzdGluZ3Vpc2hpbmcgYmV0d2VlbiBgZm9yY2Utc3RhdGljYCBhbmQgcGFnZXMgdGhhdCBoYXZlIG5vXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBwb3N0cG9uZWQgc3RhdGUuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiBkaXN0aW5ndWlzaCBgZm9yY2Utc3RhdGljYCBmcm9tIHBhZ2VzIHdpdGggbm8gcG9zdHBvbmVkIHN0YXRlIChzdGF0aWMpXG4gICAgICAgICAgICAgICAgICAgICAgICBjYWNoZUNvbnRyb2w6IGlzRHluYW1pY1JTQ1JlcXVlc3QgPyB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV2YWxpZGF0ZTogMCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgfSA6IGNhY2hlRW50cnkuY2FjaGVDb250cm9sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBBcyB0aGlzIGlzbid0IGEgcHJlZmV0Y2ggcmVxdWVzdCwgd2Ugc2hvdWxkIHNlcnZlIHRoZSBzdGF0aWMgZmxpZ2h0XG4gICAgICAgICAgICAgICAgLy8gZGF0YS5cbiAgICAgICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgICAgIHJlcSxcbiAgICAgICAgICAgICAgICAgICAgcmVzLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiAncnNjJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IFJlbmRlclJlc3VsdC5mcm9tU3RhdGljKGNhY2hlZERhdGEucnNjRGF0YSksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFRoaXMgaXMgYSByZXF1ZXN0IGZvciBIVE1MIGRhdGEuXG4gICAgICAgICAgICBsZXQgYm9keSA9IGNhY2hlZERhdGEuaHRtbDtcbiAgICAgICAgICAgIC8vIElmIHRoZXJlJ3Mgbm8gcG9zdHBvbmVkIHN0YXRlLCB3ZSBzaG91bGQganVzdCBzZXJ2ZSB0aGUgSFRNTC4gVGhpc1xuICAgICAgICAgICAgLy8gc2hvdWxkIGFsc28gYmUgdGhlIGNhc2UgZm9yIGEgcmVzdW1lIHJlcXVlc3QgYmVjYXVzZSBpdCdzIGNvbXBsZXRlZFxuICAgICAgICAgICAgLy8gYXMgYSBzZXJ2ZXIgcmVuZGVyIChyYXRoZXIgdGhhbiBhIHN0YXRpYyByZW5kZXIpLlxuICAgICAgICAgICAgaWYgKCFkaWRQb3N0cG9uZSB8fCBtaW5pbWFsTW9kZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBzZW5kUmVuZGVyUmVzdWx0KHtcbiAgICAgICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgICAgICByZXMsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6ICdodG1sJyxcbiAgICAgICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgICAgICBwb3dlcmVkQnlIZWFkZXI6IG5leHRDb25maWcucG93ZXJlZEJ5SGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IGJvZHksXG4gICAgICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDogY2FjaGVFbnRyeS5jYWNoZUNvbnRyb2xcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIElmIHdlJ3JlIGRlYnVnZ2luZyB0aGUgc3RhdGljIHNoZWxsIG9yIHRoZSBkeW5hbWljIEFQSSBhY2Nlc3Nlcywgd2VcbiAgICAgICAgICAgIC8vIHNob3VsZCBqdXN0IHNlcnZlIHRoZSBIVE1MIHdpdGhvdXQgcmVzdW1pbmcgdGhlIHJlbmRlci4gVGhlIHJldHVybmVkXG4gICAgICAgICAgICAvLyBIVE1MIHdpbGwgYmUgdGhlIHN0YXRpYyBzaGVsbCBzbyBhbGwgdGhlIER5bmFtaWMgQVBJJ3Mgd2lsbCBiZSB1c2VkXG4gICAgICAgICAgICAvLyBkdXJpbmcgc3RhdGljIGdlbmVyYXRpb24uXG4gICAgICAgICAgICBpZiAoaXNEZWJ1Z1N0YXRpY1NoZWxsIHx8IGlzRGVidWdEeW5hbWljQWNjZXNzZXMpIHtcbiAgICAgICAgICAgICAgICAvLyBTaW5jZSB3ZSdyZSBub3QgcmVzdW1pbmcgdGhlIHJlbmRlciwgd2UgbmVlZCB0byBhdCBsZWFzdCBhZGQgdGhlXG4gICAgICAgICAgICAgICAgLy8gY2xvc2luZyBib2R5IGFuZCBodG1sIHRhZ3MgdG8gY3JlYXRlIHZhbGlkIEhUTUwuXG4gICAgICAgICAgICAgICAgYm9keS5jaGFpbihuZXcgUmVhZGFibGVTdHJlYW0oe1xuICAgICAgICAgICAgICAgICAgICBzdGFydCAoY29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKEVOQ09ERURfVEFHUy5DTE9TRUQuQk9EWV9BTkRfSFRNTCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmNsb3NlKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHNlbmRSZW5kZXJSZXN1bHQoe1xuICAgICAgICAgICAgICAgICAgICByZXEsXG4gICAgICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2h0bWwnLFxuICAgICAgICAgICAgICAgICAgICBnZW5lcmF0ZUV0YWdzOiBuZXh0Q29uZmlnLmdlbmVyYXRlRXRhZ3MsXG4gICAgICAgICAgICAgICAgICAgIHBvd2VyZWRCeUhlYWRlcjogbmV4dENvbmZpZy5wb3dlcmVkQnlIZWFkZXIsXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdDogYm9keSxcbiAgICAgICAgICAgICAgICAgICAgY2FjaGVDb250cm9sOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZXhwaXJlOiB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gVGhpcyByZXF1ZXN0IGhhcyBwb3N0cG9uZWQsIHNvIGxldCdzIGNyZWF0ZSBhIG5ldyB0cmFuc2Zvcm1lciB0aGF0IHRoZVxuICAgICAgICAgICAgLy8gZHluYW1pYyBkYXRhIGNhbiBwaXBlIHRvIHRoYXQgd2lsbCBhdHRhY2ggdGhlIGR5bmFtaWMgZGF0YSB0byB0aGUgZW5kXG4gICAgICAgICAgICAvLyBvZiB0aGUgcmVzcG9uc2UuXG4gICAgICAgICAgICBjb25zdCB0cmFuc2Zvcm1lciA9IG5ldyBUcmFuc2Zvcm1TdHJlYW0oKTtcbiAgICAgICAgICAgIGJvZHkuY2hhaW4odHJhbnNmb3JtZXIucmVhZGFibGUpO1xuICAgICAgICAgICAgLy8gUGVyZm9ybSB0aGUgcmVuZGVyIGFnYWluLCBidXQgdGhpcyB0aW1lLCBwcm92aWRlIHRoZSBwb3N0cG9uZWQgc3RhdGUuXG4gICAgICAgICAgICAvLyBXZSBkb24ndCBhd2FpdCBiZWNhdXNlIHdlIHdhbnQgdGhlIHJlc3VsdCB0byBzdGFydCBzdHJlYW1pbmcgbm93LCBhbmRcbiAgICAgICAgICAgIC8vIHdlJ3ZlIGFscmVhZHkgY2hhaW5lZCB0aGUgdHJhbnNmb3JtZXIncyByZWFkYWJsZSB0byB0aGUgcmVuZGVyIHJlc3VsdC5cbiAgICAgICAgICAgIGRvUmVuZGVyKHtcbiAgICAgICAgICAgICAgICBzcGFuLFxuICAgICAgICAgICAgICAgIHBvc3Rwb25lZDogY2FjaGVkRGF0YS5wb3N0cG9uZWQsXG4gICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBhIHJlc3VtZSByZW5kZXIsIG5vdCBhIGZhbGxiYWNrIHJlbmRlciwgc28gd2UgZG9uJ3QgbmVlZCB0b1xuICAgICAgICAgICAgICAgIC8vIHNldCB0aGlzLlxuICAgICAgICAgICAgICAgIGZhbGxiYWNrUm91dGVQYXJhbXM6IG51bGxcbiAgICAgICAgICAgIH0pLnRoZW4oYXN5bmMgKHJlc3VsdCk9PntcbiAgICAgICAgICAgICAgICB2YXIgX3Jlc3VsdF92YWx1ZTtcbiAgICAgICAgICAgICAgICBpZiAoIXJlc3VsdCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKCdJbnZhcmlhbnQ6IGV4cGVjdGVkIGEgcmVzdWx0IHRvIGJlIHJldHVybmVkJyksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFwiRTQ2M1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICgoKF9yZXN1bHRfdmFsdWUgPSByZXN1bHQudmFsdWUpID09IG51bGwgPyB2b2lkIDAgOiBfcmVzdWx0X3ZhbHVlLmtpbmQpICE9PSBDYWNoZWRSb3V0ZUtpbmQuQVBQX1BBR0UpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9yZXN1bHRfdmFsdWUxO1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IEVycm9yKGBJbnZhcmlhbnQ6IGV4cGVjdGVkIGEgcGFnZSByZXNwb25zZSwgZ290ICR7KF9yZXN1bHRfdmFsdWUxID0gcmVzdWx0LnZhbHVlKSA9PSBudWxsID8gdm9pZCAwIDogX3Jlc3VsdF92YWx1ZTEua2luZH1gKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJFMzA1XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gUGlwZSB0aGUgcmVzdW1lIHJlc3VsdCB0byB0aGUgdHJhbnNmb3JtZXIuXG4gICAgICAgICAgICAgICAgYXdhaXQgcmVzdWx0LnZhbHVlLmh0bWwucGlwZVRvKHRyYW5zZm9ybWVyLndyaXRhYmxlKTtcbiAgICAgICAgICAgIH0pLmNhdGNoKChlcnIpPT57XG4gICAgICAgICAgICAgICAgLy8gQW4gZXJyb3Igb2NjdXJyZWQgZHVyaW5nIHBpcGluZyBvciBwcmVwYXJpbmcgdGhlIHJlbmRlciwgYWJvcnRcbiAgICAgICAgICAgICAgICAvLyB0aGUgdHJhbnNmb3JtZXJzIHdyaXRlciBzbyB3ZSBjYW4gdGVybWluYXRlIHRoZSBzdHJlYW0uXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtZXIud3JpdGFibGUuYWJvcnQoZXJyKS5jYXRjaCgoZSk9PntcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcImNvdWxkbid0IGFib3J0IHRyYW5zZm9ybWVyXCIsIGUpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gc2VuZFJlbmRlclJlc3VsdCh7XG4gICAgICAgICAgICAgICAgcmVxLFxuICAgICAgICAgICAgICAgIHJlcyxcbiAgICAgICAgICAgICAgICB0eXBlOiAnaHRtbCcsXG4gICAgICAgICAgICAgICAgZ2VuZXJhdGVFdGFnczogbmV4dENvbmZpZy5nZW5lcmF0ZUV0YWdzLFxuICAgICAgICAgICAgICAgIHBvd2VyZWRCeUhlYWRlcjogbmV4dENvbmZpZy5wb3dlcmVkQnlIZWFkZXIsXG4gICAgICAgICAgICAgICAgcmVzdWx0OiBib2R5LFxuICAgICAgICAgICAgICAgIC8vIFdlIGRvbid0IHdhbnQgdG8gY2FjaGUgdGhlIHJlc3BvbnNlIGlmIGl0IGhhcyBwb3N0cG9uZWQgZGF0YSBiZWNhdXNlXG4gICAgICAgICAgICAgICAgLy8gdGhlIHJlc3BvbnNlIGJlaW5nIHNlbnQgdG8gdGhlIGNsaWVudCBpdCdzIGR5bmFtaWMgcGFydHMgYXJlIHN0cmVhbWVkXG4gICAgICAgICAgICAgICAgLy8gdG8gdGhlIGNsaWVudCBvbiB0aGUgc2FtZSByZXF1ZXN0LlxuICAgICAgICAgICAgICAgIGNhY2hlQ29udHJvbDoge1xuICAgICAgICAgICAgICAgICAgICByZXZhbGlkYXRlOiAwLFxuICAgICAgICAgICAgICAgICAgICBleHBpcmU6IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgICAgICAvLyBUT0RPOiBhY3RpdmVTcGFuIGNvZGUgcGF0aCBpcyBmb3Igd2hlbiB3cmFwcGVkIGJ5XG4gICAgICAgIC8vIG5leHQtc2VydmVyIGNhbiBiZSByZW1vdmVkIHdoZW4gdGhpcyBpcyBubyBsb25nZXIgdXNlZFxuICAgICAgICBpZiAoYWN0aXZlU3Bhbikge1xuICAgICAgICAgICAgYXdhaXQgaGFuZGxlUmVzcG9uc2UoYWN0aXZlU3Bhbik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgdHJhY2VyLndpdGhQcm9wYWdhdGVkQ29udGV4dChyZXEuaGVhZGVycywgKCk9PnRyYWNlci50cmFjZShCYXNlU2VydmVyU3Bhbi5oYW5kbGVSZXF1ZXN0LCB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW5OYW1lOiBgJHttZXRob2R9ICR7cmVxLnVybH1gLFxuICAgICAgICAgICAgICAgICAgICBraW5kOiBTcGFuS2luZC5TRVJWRVIsXG4gICAgICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLm1ldGhvZCc6IG1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICdodHRwLnRhcmdldCc6IHJlcS51cmxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIGhhbmRsZVJlc3BvbnNlKSk7XG4gICAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgLy8gaWYgd2UgYXJlbid0IHdyYXBwZWQgYnkgYmFzZS1zZXJ2ZXIgaGFuZGxlIGhlcmVcbiAgICAgICAgaWYgKCFhY3RpdmVTcGFuICYmICEoZXJyIGluc3RhbmNlb2YgTm9GYWxsYmFja0Vycm9yKSkge1xuICAgICAgICAgICAgYXdhaXQgcm91dGVNb2R1bGUub25SZXF1ZXN0RXJyb3IocmVxLCBlcnIsIHtcbiAgICAgICAgICAgICAgICByb3V0ZXJLaW5kOiAnQXBwIFJvdXRlcicsXG4gICAgICAgICAgICAgICAgcm91dGVQYXRoOiBzcmNQYWdlLFxuICAgICAgICAgICAgICAgIHJvdXRlVHlwZTogJ3JlbmRlcicsXG4gICAgICAgICAgICAgICAgcmV2YWxpZGF0ZVJlYXNvbjogZ2V0UmV2YWxpZGF0ZVJlYXNvbih7XG4gICAgICAgICAgICAgICAgICAgIGlzUmV2YWxpZGF0ZTogaXNTU0csXG4gICAgICAgICAgICAgICAgICAgIGlzT25EZW1hbmRSZXZhbGlkYXRlXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH0sIHJvdXRlclNlcnZlckNvbnRleHQpO1xuICAgICAgICB9XG4gICAgICAgIC8vIHJldGhyb3cgc28gdGhhdCB3ZSBjYW4gaGFuZGxlIHNlcnZpbmcgZXJyb3IgcGFnZVxuICAgICAgICB0aHJvdyBlcnI7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q29rcnR3JTVDJTVDRGVza3RvcCU1QyU1Q1BvcnRmb2xpbyU1QyU1Q2phbm1lamF5LXRpd2FyaS1wb3J0Zm9saW8lNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxva3J0d1xcXFxEZXNrdG9wXFxcXFBvcnRmb2xpb1xcXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxva3J0d1xcRGVza3RvcFxcUG9ydGZvbGlvXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./lib/data.ts\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./components/ui/Button.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./components/Hero.tsx\");\n/* harmony import */ var _components_WorkExperience__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/WorkExperience */ \"(ssr)/./components/WorkExperience.tsx\");\n/* harmony import */ var _components_Projects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Projects */ \"(ssr)/./components/Projects.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WorkExperience__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Projects__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contact\",\n                className: \"min-h-screen bg-background flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-textPrimary mb-4\",\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-textSecondary\",\n                            children: \"This section will be implemented in a later phase\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold mb-4 text-accent\",\n                            children: \"Development Testing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"bg-card p-6 rounded-lg mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                    children: \"Button Component Test\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"primary\",\n                                            onClick: ()=>alert('Primary button clicked!'),\n                                            children: \"Primary Button\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"outline\",\n                                            onClick: ()=>alert('Outline button clicked!'),\n                                            children: \"Outline Button\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"primary\",\n                                            href: \"#work-experience\",\n                                            children: \"Link Button\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: \"Disabled Button\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                    className: \"bg-card p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                            children: \"Hero Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 52\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.heroData.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Bio:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 52\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.heroData.bio\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"GitHub:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 52\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.heroData.socialLinks.github\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                    className: \"bg-card p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                            children: \"Work Experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 13\n                                        }, this),\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.workExperience.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-4 bg-background rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-accent\",\n                                                        children: job.companyName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-textSecondary\",\n                                                        children: [\n                                                            job.role,\n                                                            \" | \",\n                                                            job.timeline\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, job.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                    className: \"bg-card p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                            children: [\n                                                \"Projects (\",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.projects.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 13\n                                        }, this),\n                                        _lib_data__WEBPACK_IMPORTED_MODULE_1__.projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-4 bg-background rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-accent\",\n                                                        children: project.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-textSecondary mb-2\",\n                                                        children: project.purpose\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-textSecondary mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Learnings:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                                lineNumber: 79,\n                                                                columnNumber: 56\n                                                            }, this),\n                                                            \" \",\n                                                            project.learnings\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-textSecondary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"GitHub:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                                lineNumber: 80,\n                                                                columnNumber: 51\n                                                            }, this),\n                                                            \" \",\n                                                            project.githubLink\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, project.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                    className: \"bg-card p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                            children: [\n                                                \"Certificates (\",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.certificates.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: _lib_data__WEBPACK_IMPORTED_MODULE_1__.certificates.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-background rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-accent\",\n                                                            children: cert.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-textSecondary\",\n                                                            children: [\n                                                                \"Issued by: \",\n                                                                cert.issuerName\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, cert.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                    className: \"bg-card p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold mb-4 text-textPrimary\",\n                                            children: \"Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Degree:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 52\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.degree\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Institution:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 52\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.institution\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Timeline:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 52\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.timeline\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"CGPA:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 47\n                                                }, this),\n                                                \" \",\n                                                _lib_data__WEBPACK_IMPORTED_MODULE_1__.education.detail\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\app\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRXlGO0FBQzdDO0FBQ1A7QUFDb0I7QUFDWjtBQUU5QixTQUFTUztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNMLHdEQUFJQTs7Ozs7MEJBR0wsOERBQUNDLGtFQUFjQTs7Ozs7MEJBR2YsOERBQUNDLDREQUFRQTs7Ozs7MEJBRVQsOERBQUNJO2dCQUFRQyxJQUFHO2dCQUFVRixXQUFVOzBCQUM5Qiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFBR0gsV0FBVTtzQ0FBMkM7Ozs7OztzQ0FDekQsOERBQUNJOzRCQUFFSixXQUFVO3NDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS3RDLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0s7b0JBQUtMLFdBQVU7O3NDQUNkLDhEQUFDTTs0QkFBR04sV0FBVTtzQ0FBc0M7Ozs7OztzQ0FHcEQsOERBQUNDOzRCQUFRRCxXQUFVOzs4Q0FDakIsOERBQUNHO29DQUFHSCxXQUFVOzhDQUErQzs7Ozs7OzhDQUM3RCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTiw2REFBTUE7NENBQUNhLFNBQVE7NENBQVVDLFNBQVMsSUFBTUMsTUFBTTtzREFBNEI7Ozs7OztzREFHM0UsOERBQUNmLDZEQUFNQTs0Q0FBQ2EsU0FBUTs0Q0FBVUMsU0FBUyxJQUFNQyxNQUFNO3NEQUE0Qjs7Ozs7O3NEQUczRSw4REFBQ2YsNkRBQU1BOzRDQUFDYSxTQUFROzRDQUFVRyxNQUFLO3NEQUFtQjs7Ozs7O3NEQUdsRCw4REFBQ2hCLDZEQUFNQTs0Q0FBQ2EsU0FBUTs0Q0FBVUksUUFBUTtzREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU16Qyw4REFBQ1o7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDQztvQ0FBUUQsV0FBVTs7c0RBQ2pCLDhEQUFDRzs0Q0FBR0gsV0FBVTtzREFBK0M7Ozs7OztzREFDN0QsOERBQUNJOzRDQUFFSixXQUFVOzs4REFBMEIsOERBQUNZOzhEQUFPOzs7Ozs7Z0RBQWM7Z0RBQUV2QiwrQ0FBUUEsQ0FBQ3dCLElBQUk7Ozs7Ozs7c0RBQzVFLDhEQUFDVDs0Q0FBRUosV0FBVTs7OERBQTBCLDhEQUFDWTs4REFBTzs7Ozs7O2dEQUFhO2dEQUFFdkIsK0NBQVFBLENBQUN5QixHQUFHOzs7Ozs7O3NEQUMxRSw4REFBQ1Y7NENBQUVKLFdBQVU7OzhEQUEwQiw4REFBQ1k7OERBQU87Ozs7OztnREFBZ0I7Z0RBQUV2QiwrQ0FBUUEsQ0FBQzBCLFdBQVcsQ0FBQ0MsTUFBTTs7Ozs7Ozs7Ozs7Ozs4Q0FJOUYsOERBQUNmO29DQUFRRCxXQUFVOztzREFDakIsOERBQUNHOzRDQUFHSCxXQUFVO3NEQUErQzs7Ozs7O3dDQUM1RFYscURBQWNBLENBQUMyQixHQUFHLENBQUMsQ0FBQ0Msb0JBQ25CLDhEQUFDbkI7Z0RBQWlCQyxXQUFVOztrRUFDMUIsOERBQUNtQjt3REFBR25CLFdBQVU7a0VBQW1Da0IsSUFBSUUsV0FBVzs7Ozs7O2tFQUNoRSw4REFBQ2hCO3dEQUFFSixXQUFVOzs0REFBc0JrQixJQUFJRyxJQUFJOzREQUFDOzREQUFJSCxJQUFJSSxRQUFROzs7Ozs7OzsrQ0FGcERKLElBQUloQixFQUFFOzs7Ozs7Ozs7Ozs4Q0FRcEIsOERBQUNEO29DQUFRRCxXQUFVOztzREFDakIsOERBQUNHOzRDQUFHSCxXQUFVOztnREFBK0M7Z0RBQVdSLCtDQUFRQSxDQUFDK0IsTUFBTTtnREFBQzs7Ozs7Ozt3Q0FDdkYvQiwrQ0FBUUEsQ0FBQ3lCLEdBQUcsQ0FBQyxDQUFDTyx3QkFDYiw4REFBQ3pCO2dEQUFxQkMsV0FBVTs7a0VBQzlCLDhEQUFDbUI7d0RBQUduQixXQUFVO2tFQUFtQ3dCLFFBQVFYLElBQUk7Ozs7OztrRUFDN0QsOERBQUNUO3dEQUFFSixXQUFVO2tFQUEyQndCLFFBQVFDLE9BQU87Ozs7OztrRUFDdkQsOERBQUNyQjt3REFBRUosV0FBVTs7MEVBQTBCLDhEQUFDWTswRUFBTzs7Ozs7OzREQUFtQjs0REFBRVksUUFBUUUsU0FBUzs7Ozs7OztrRUFDckYsOERBQUN0Qjt3REFBRUosV0FBVTs7MEVBQXFCLDhEQUFDWTswRUFBTzs7Ozs7OzREQUFnQjs0REFBRVksUUFBUUcsVUFBVTs7Ozs7Ozs7K0NBSnRFSCxRQUFRdEIsRUFBRTs7Ozs7Ozs7Ozs7OENBVXhCLDhEQUFDRDtvQ0FBUUQsV0FBVTs7c0RBQ2pCLDhEQUFDRzs0Q0FBR0gsV0FBVTs7Z0RBQStDO2dEQUFlUCxtREFBWUEsQ0FBQzhCLE1BQU07Z0RBQUM7Ozs7Ozs7c0RBQ2hHLDhEQUFDeEI7NENBQUlDLFdBQVU7c0RBQ1pQLG1EQUFZQSxDQUFDd0IsR0FBRyxDQUFDLENBQUNXLHFCQUNqQiw4REFBQzdCO29EQUFrQkMsV0FBVTs7c0VBQzNCLDhEQUFDbUI7NERBQUduQixXQUFVO3NFQUFtQzRCLEtBQUtmLElBQUk7Ozs7OztzRUFDMUQsOERBQUNUOzREQUFFSixXQUFVOztnRUFBcUI7Z0VBQVk0QixLQUFLQyxVQUFVOzs7Ozs7OzttREFGckRELEtBQUsxQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhDQVN2Qiw4REFBQ0Q7b0NBQVFELFdBQVU7O3NEQUNqQiw4REFBQ0c7NENBQUdILFdBQVU7c0RBQStDOzs7Ozs7c0RBQzdELDhEQUFDSTs0Q0FBRUosV0FBVTs7OERBQTBCLDhEQUFDWTs4REFBTzs7Ozs7O2dEQUFnQjtnREFBRXJCLGdEQUFTQSxDQUFDdUMsTUFBTTs7Ozs7OztzREFDakYsOERBQUMxQjs0Q0FBRUosV0FBVTs7OERBQTBCLDhEQUFDWTs4REFBTzs7Ozs7O2dEQUFxQjtnREFBRXJCLGdEQUFTQSxDQUFDd0MsV0FBVzs7Ozs7OztzREFDM0YsOERBQUMzQjs0Q0FBRUosV0FBVTs7OERBQTBCLDhEQUFDWTs4REFBTzs7Ozs7O2dEQUFrQjtnREFBRXJCLGdEQUFTQSxDQUFDK0IsUUFBUTs7Ozs7OztzREFDckYsOERBQUNsQjs0Q0FBRUosV0FBVTs7OERBQXFCLDhEQUFDWTs4REFBTzs7Ozs7O2dEQUFjO2dEQUFFckIsZ0RBQVNBLENBQUN5QyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdEYiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcb2tydHdcXERlc2t0b3BcXFBvcnRmb2xpb1xcamFubWVqYXktdGl3YXJpLXBvcnRmb2xpb1xcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IGhlcm9EYXRhLCB3b3JrRXhwZXJpZW5jZSwgZWR1Y2F0aW9uLCBwcm9qZWN0cywgY2VydGlmaWNhdGVzIH0gZnJvbSBcIkAvbGliL2RhdGFcIjtcbmltcG9ydCBCdXR0b24gZnJvbSBcIkAvY29tcG9uZW50cy91aS9CdXR0b25cIjtcbmltcG9ydCBIZXJvIGZyb20gXCJAL2NvbXBvbmVudHMvSGVyb1wiO1xuaW1wb3J0IFdvcmtFeHBlcmllbmNlIGZyb20gXCJAL2NvbXBvbmVudHMvV29ya0V4cGVyaWVuY2VcIjtcbmltcG9ydCBQcm9qZWN0cyBmcm9tIFwiQC9jb21wb25lbnRzL1Byb2plY3RzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW5cIj5cbiAgICAgIHsvKiBIZXJvIFNlY3Rpb24gKi99XG4gICAgICA8SGVybyAvPlxuXG4gICAgICB7LyogV29yayBFeHBlcmllbmNlIFNlY3Rpb24gKi99XG4gICAgICA8V29ya0V4cGVyaWVuY2UgLz5cblxuICAgICAgey8qIFByb2plY3RzIFNlY3Rpb24gKi99XG4gICAgICA8UHJvamVjdHMgLz5cblxuICAgICAgPHNlY3Rpb24gaWQ9XCJjb250YWN0XCIgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJhY2tncm91bmQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC10ZXh0UHJpbWFyeSBtYi00XCI+Q29udGFjdDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0U2Vjb25kYXJ5XCI+VGhpcyBzZWN0aW9uIHdpbGwgYmUgaW1wbGVtZW50ZWQgaW4gYSBsYXRlciBwaGFzZTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBEZXZlbG9wbWVudCBUZXN0aW5nIFNlY3Rpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOFwiPlxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgbWItNCB0ZXh0LWFjY2VudFwiPkRldmVsb3BtZW50IFRlc3Rpbmc8L2gxPlxuXG4gICAgICAgICAgey8qIEJ1dHRvbiBDb21wb25lbnQgVGVzdCAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJiZy1jYXJkIHAtNiByb3VuZGVkLWxnIG1iLThcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTQgdGV4dC10ZXh0UHJpbWFyeVwiPkJ1dHRvbiBDb21wb25lbnQgVGVzdDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cInByaW1hcnlcIiBvbkNsaWNrPXsoKSA9PiBhbGVydCgnUHJpbWFyeSBidXR0b24gY2xpY2tlZCEnKX0+XG4gICAgICAgICAgICAgICAgUHJpbWFyeSBCdXR0b25cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBhbGVydCgnT3V0bGluZSBidXR0b24gY2xpY2tlZCEnKX0+XG4gICAgICAgICAgICAgICAgT3V0bGluZSBCdXR0b25cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cInByaW1hcnlcIiBocmVmPVwiI3dvcmstZXhwZXJpZW5jZVwiPlxuICAgICAgICAgICAgICAgIExpbmsgQnV0dG9uXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgZGlzYWJsZWQ+XG4gICAgICAgICAgICAgICAgRGlzYWJsZWQgQnV0dG9uXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgey8qIEhlcm8gRGF0YSBUZXN0ICovfVxuICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLWNhcmQgcC02IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTQgdGV4dC10ZXh0UHJpbWFyeVwiPkhlcm8gRGF0YTwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHRTZWNvbmRhcnkgbWItMlwiPjxzdHJvbmc+TmFtZTo8L3N0cm9uZz4ge2hlcm9EYXRhLm5hbWV9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0U2Vjb25kYXJ5IG1iLTJcIj48c3Ryb25nPkJpbzo8L3N0cm9uZz4ge2hlcm9EYXRhLmJpb308L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHRTZWNvbmRhcnkgbWItMlwiPjxzdHJvbmc+R2l0SHViOjwvc3Ryb25nPiB7aGVyb0RhdGEuc29jaWFsTGlua3MuZ2l0aHVifTwvcD5cbiAgICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgICB7LyogV29yayBFeHBlcmllbmNlIFRlc3QgKi99XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiYmctY2FyZCBwLTYgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNCB0ZXh0LXRleHRQcmltYXJ5XCI+V29yayBFeHBlcmllbmNlPC9oMj5cbiAgICAgICAgICAgIHt3b3JrRXhwZXJpZW5jZS5tYXAoKGpvYikgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17am9iLmlkfSBjbGFzc05hbWU9XCJtYi00IHAtNCBiZy1iYWNrZ3JvdW5kIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWFjY2VudFwiPntqb2IuY29tcGFueU5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHRTZWNvbmRhcnlcIj57am9iLnJvbGV9IHwge2pvYi50aW1lbGluZX08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgey8qIFByb2plY3RzIFRlc3QgKi99XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiYmctY2FyZCBwLTYgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNCB0ZXh0LXRleHRQcmltYXJ5XCI+UHJvamVjdHMgKHtwcm9qZWN0cy5sZW5ndGh9KTwvaDI+XG4gICAgICAgICAgICB7cHJvamVjdHMubWFwKChwcm9qZWN0KSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtwcm9qZWN0LmlkfSBjbGFzc05hbWU9XCJtYi00IHAtNCBiZy1iYWNrZ3JvdW5kIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWFjY2VudFwiPntwcm9qZWN0Lm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHRTZWNvbmRhcnkgbWItMlwiPntwcm9qZWN0LnB1cnBvc2V9PC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtdGV4dFNlY29uZGFyeSBtYi0yXCI+PHN0cm9uZz5MZWFybmluZ3M6PC9zdHJvbmc+IHtwcm9qZWN0LmxlYXJuaW5nc308L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0U2Vjb25kYXJ5XCI+PHN0cm9uZz5HaXRIdWI6PC9zdHJvbmc+IHtwcm9qZWN0LmdpdGh1Ykxpbmt9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgIHsvKiBDZXJ0aWZpY2F0ZXMgVGVzdCAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJiZy1jYXJkIHAtNiByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBtYi00IHRleHQtdGV4dFByaW1hcnlcIj5DZXJ0aWZpY2F0ZXMgKHtjZXJ0aWZpY2F0ZXMubGVuZ3RofSk8L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgIHtjZXJ0aWZpY2F0ZXMubWFwKChjZXJ0KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2NlcnQuaWR9IGNsYXNzTmFtZT1cInAtNCBiZy1iYWNrZ3JvdW5kIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtYWNjZW50XCI+e2NlcnQubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0U2Vjb25kYXJ5XCI+SXNzdWVkIGJ5OiB7Y2VydC5pc3N1ZXJOYW1lfTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgICB7LyogRWR1Y2F0aW9uIFRlc3QgKi99XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiYmctY2FyZCBwLTYgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNCB0ZXh0LXRleHRQcmltYXJ5XCI+RWR1Y2F0aW9uPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtdGV4dFNlY29uZGFyeSBtYi0yXCI+PHN0cm9uZz5EZWdyZWU6PC9zdHJvbmc+IHtlZHVjYXRpb24uZGVncmVlfTwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtdGV4dFNlY29uZGFyeSBtYi0yXCI+PHN0cm9uZz5JbnN0aXR1dGlvbjo8L3N0cm9uZz4ge2VkdWNhdGlvbi5pbnN0aXR1dGlvbn08L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXRleHRTZWNvbmRhcnkgbWItMlwiPjxzdHJvbmc+VGltZWxpbmU6PC9zdHJvbmc+IHtlZHVjYXRpb24udGltZWxpbmV9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0U2Vjb25kYXJ5XCI+PHN0cm9uZz5DR1BBOjwvc3Ryb25nPiB7ZWR1Y2F0aW9uLmRldGFpbH08L3A+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaGVyb0RhdGEiLCJ3b3JrRXhwZXJpZW5jZSIsImVkdWNhdGlvbiIsInByb2plY3RzIiwiY2VydGlmaWNhdGVzIiwiQnV0dG9uIiwiSGVybyIsIldvcmtFeHBlcmllbmNlIiwiUHJvamVjdHMiLCJIb21lIiwiZGl2IiwiY2xhc3NOYW1lIiwic2VjdGlvbiIsImlkIiwiaDIiLCJwIiwibWFpbiIsImgxIiwidmFyaWFudCIsIm9uQ2xpY2siLCJhbGVydCIsImhyZWYiLCJkaXNhYmxlZCIsInN0cm9uZyIsIm5hbWUiLCJiaW8iLCJzb2NpYWxMaW5rcyIsImdpdGh1YiIsIm1hcCIsImpvYiIsImgzIiwiY29tcGFueU5hbWUiLCJyb2xlIiwidGltZWxpbmUiLCJsZW5ndGgiLCJwcm9qZWN0IiwicHVycG9zZSIsImxlYXJuaW5ncyIsImdpdGh1YkxpbmsiLCJjZXJ0IiwiaXNzdWVyTmFtZSIsImRlZ3JlZSIsImluc3RpdHV0aW9uIiwiZGV0YWlsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ExperienceCard.tsx":
/*!***************************************!*\
  !*** ./components/ExperienceCard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExperienceCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Animation variants for the card - Following step 5.3 specification\nconst cardVariants = {\n    hidden: {\n        opacity: 0,\n        x: -50\n    },\n    visible: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.5,\n            ease: 'easeOut'\n        }\n    }\n};\n// Animation variants for the details list\nconst listVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst listItemVariants = {\n    hidden: {\n        opacity: 0,\n        x: -20\n    },\n    visible: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.4,\n            ease: 'easeOut'\n        }\n    }\n};\nfunction ExperienceCard({ experience, index }) {\n    const { companyName, companyLogo, role, timeline, details, certificateLink } = experience;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        variants: cardVariants,\n        initial: \"hidden\",\n        whileInView: \"visible\",\n        viewport: {\n            once: true,\n            amount: 0.3\n        },\n        className: \"bg-card border border-border rounded-xl p-8 shadow-lg\",\n        whileHover: {\n            y: -5,\n            boxShadow: '0px 10px 30px -15px rgba(2,12,27,0.7)',\n            transition: {\n                duration: 0.2\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 relative bg-white rounded-lg p-2 shadow-sm flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            src: companyLogo,\n                            alt: `${companyName} logo`,\n                            fill: true,\n                            className: \"object-contain\",\n                            sizes: \"64px\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl md:text-2xl font-bold text-textPrimary\",\n                        children: companyName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg md:text-xl font-semibold text-accent\",\n                        children: role\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-textSecondary font-medium\",\n                        children: timeline\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.ul, {\n                variants: listVariants,\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true,\n                    amount: 0.5\n                },\n                className: \"space-y-3 mb-6\",\n                children: details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.li, {\n                        variants: listItemVariants,\n                        className: \"flex items-start gap-3 text-textSecondary leading-relaxed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: detail\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, detailIndex, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            certificateLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 10\n                },\n                whileInView: {\n                    opacity: 1,\n                    y: 0\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    delay: 0.4,\n                    duration: 0.3\n                },\n                className: \"flex justify-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    variant: \"outline\",\n                    href: certificateLink,\n                    className: \"text-sm\",\n                    children: \"View Certificate\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ExperienceCard.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ExperienceCard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FaGithub_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FaGithub,FaLinkedin,FaTwitter!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./components/ui/Button.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./lib/data.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Animation variants for staggered reveal\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: 'easeOut'\n        }\n    }\n};\n// Social media icon mapping\nconst socialIcons = {\n    github: _barrel_optimize_names_FaGithub_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaGithub,\n    linkedin: _barrel_optimize_names_FaGithub_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaLinkedin,\n    twitter: _barrel_optimize_names_FaGithub_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaTwitter\n};\nfunction Hero() {\n    // Smooth scroll to work experience section\n    const scrollToWork = ()=>{\n        const workSection = document.getElementById('work-experience');\n        if (workSection) {\n            workSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Smooth scroll to contact section\n    const scrollToContact = ()=>{\n        const contactSection = document.getElementById('contact');\n        if (contactSection) {\n            contactSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.section, {\n        className: \"min-h-screen flex items-center justify-center px-6 py-12\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8 order-2 md:order-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                className: \"text-4xl md:text-6xl font-bold text-textPrimary leading-tight\",\n                                variants: itemVariants,\n                                children: _lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                className: \"text-lg md:text-xl text-textSecondary leading-relaxed max-w-lg\",\n                                variants: itemVariants,\n                                children: _lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.bio\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                variants: itemVariants,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"primary\",\n                                        onClick: scrollToWork,\n                                        className: \"w-full sm:w-auto\",\n                                        children: _lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.buttons.viewWork\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"outline\",\n                                        href: _lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.resumeLink,\n                                        className: \"w-full sm:w-auto\",\n                                        children: _lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.buttons.viewResume\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"flex gap-6\",\n                                variants: itemVariants,\n                                children: [\n                                    Object.entries(_lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.socialLinks).map(([platform, url])=>{\n                                        const IconComponent = socialIcons[platform];\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.a, {\n                                            href: url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-textSecondary hover:text-accent transition-colors duration-200\",\n                                            whileHover: {\n                                                scale: 1.2,\n                                                color: 'var(--accent)'\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            transition: {\n                                                type: 'spring',\n                                                stiffness: 400,\n                                                damping: 17\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    size: 28\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: platform\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, platform, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: scrollToContact,\n                                        className: \"text-textSecondary hover:text-accent transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-background rounded\",\n                                        whileHover: {\n                                            scale: 1.2,\n                                            color: 'var(--accent)'\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        transition: {\n                                            type: 'spring',\n                                            stiffness: 400,\n                                            damping: 17\n                                        },\n                                        \"aria-label\": \"Scroll to contact section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"28\",\n                                                height: \"28\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"inline-block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex justify-center order-1 md:order-2\",\n                        variants: itemVariants,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-accent/10 rounded-full blur-3xl scale-110 -z-10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-80 h-80 md:w-96 md:h-96 rounded-full overflow-hidden border-4 border-accent/20 shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: _lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.profileImage,\n                                        alt: `${_lib_data__WEBPACK_IMPORTED_MODULE_3__.heroData.name} - Profile Photo`,\n                                        fill: true,\n                                        className: \"object-cover\",\n                                        priority: true,\n                                        sizes: \"(max-width: 768px) 320px, 384px\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"absolute inset-0 rounded-full border-2 border-accent/30\",\n                                    animate: {\n                                        scale: [\n                                            1,\n                                            1.05,\n                                            1\n                                        ],\n                                        opacity: [\n                                            0.3,\n                                            0.6,\n                                            0.3\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity,\n                                        ease: 'easeInOut'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Hero.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ProjectCard.tsx":
/*!************************************!*\
  !*** ./components/ProjectCard.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Animation variants for the card\nconst cardVariants = {\n    hidden: {\n        opacity: 0,\n        y: 50\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: 'easeOut'\n        }\n    }\n};\n// Animation variants for tech stack pills\nconst pillVariants = {\n    hidden: {\n        opacity: 0,\n        scale: 0.8\n    },\n    visible: {\n        opacity: 1,\n        scale: 1,\n        transition: {\n            duration: 0.3,\n            ease: 'easeOut'\n        }\n    }\n};\n// Animation variants for the tech stack container\nconst techStackVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.3\n        }\n    }\n};\nfunction ProjectCard({ project, index }) {\n    const { name, purpose, techStack, learnings, githubLink } = project;\n    // Split tech stack into individual technologies\n    const techArray = techStack.split(', ').filter((tech)=>tech.trim() !== '');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        variants: cardVariants,\n        initial: \"hidden\",\n        whileInView: \"visible\",\n        viewport: {\n            once: true,\n            amount: 0.3\n        },\n        className: \"bg-card border border-border rounded-xl p-8 shadow-lg h-full flex flex-col\",\n        whileHover: {\n            y: -5,\n            boxShadow: '0px 10px 30px -15px rgba(2,12,27,0.7)',\n            transition: {\n                duration: 0.2\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl md:text-3xl font-bold text-textPrimary mb-4\",\n                children: name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-textSecondary mb-6 leading-relaxed flex-grow\",\n                children: purpose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            techArray.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                variants: techStackVariants,\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true,\n                    amount: 0.3\n                },\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-textPrimary mb-3 uppercase tracking-wide\",\n                        children: \"Tech Stack\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: techArray.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.span, {\n                                variants: pillVariants,\n                                className: \"px-3 py-1 bg-accent/10 text-accent text-sm font-medium rounded-full border border-accent/20 hover:bg-accent/20 transition-colors duration-200\",\n                                children: tech.trim()\n                            }, techIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-textPrimary mb-3 uppercase tracking-wide\",\n                        children: \"What I Learned\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-textSecondary text-sm leading-relaxed\",\n                        children: learnings\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: githubLink,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"inline-flex items-center gap-2 text-accent hover:text-accent/80 transition-colors duration-200 group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: \"View on GitHub\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ProjectCard.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ProjectCard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Projects.tsx":
/*!*********************************!*\
  !*** ./components/Projects.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./lib/data.ts\");\n/* harmony import */ var _components_ProjectCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProjectCard */ \"(ssr)/./components/ProjectCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Projects() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"min-h-screen bg-background py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-textPrimary mb-4\",\n                            children: \"Project Work\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-textSecondary max-w-2xl mx-auto\",\n                            children: \"A showcase of practical skills and tangible accomplishments through real-world projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: _lib_data__WEBPACK_IMPORTED_MODULE_1__.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            project: project,\n                            index: index\n                        }, project.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\Projects.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Projects.tsx\n");

/***/ }),

/***/ "(ssr)/./components/WorkExperience.tsx":
/*!***************************************!*\
  !*** ./components/WorkExperience.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkExperience)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./lib/data.ts\");\n/* harmony import */ var _ExperienceCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ExperienceCard */ \"(ssr)/./components/ExperienceCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Animation variants for the section\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2,\n            delayChildren: 0.1\n        }\n    }\n};\nconst titleVariants = {\n    hidden: {\n        opacity: 0,\n        y: 30\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: 'easeOut'\n        }\n    }\n};\nfunction WorkExperience() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.section, {\n        id: \"work-experience\",\n        className: \"py-20 bg-background\",\n        initial: \"hidden\",\n        whileInView: \"visible\",\n        viewport: {\n            once: true,\n            amount: 0.2\n        },\n        variants: containerVariants,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                        className: \"text-4xl md:text-5xl font-bold text-textPrimary mb-4 text-center\",\n                        variants: titleVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true,\n                            amount: 0.2\n                        },\n                        children: \"Work Experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                        className: \"text-lg text-textSecondary max-w-2xl mx-auto text-center mb-16\",\n                        variants: titleVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true,\n                            amount: 0.2\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        children: \"Professional journey and key contributions in software development\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: _lib_data__WEBPACK_IMPORTED_MODULE_1__.workExperience.map((experience, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExperienceCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                experience: experience,\n                                index: index\n                            }, experience.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\WorkExperience.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1dvcmtFeHBlcmllbmNlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXVDO0FBQ0s7QUFDRTtBQUU5QyxxQ0FBcUM7QUFDckMsTUFBTUcsb0JBQW9CO0lBQ3hCQyxRQUFRO1FBQUVDLFNBQVM7SUFBRTtJQUNyQkMsU0FBUztRQUNQRCxTQUFTO1FBQ1RFLFlBQVk7WUFDVkMsaUJBQWlCO1lBQ2pCQyxlQUFlO1FBQ2pCO0lBQ0Y7QUFDRjtBQUVBLE1BQU1DLGdCQUFnQjtJQUNwQk4sUUFBUTtRQUNOQyxTQUFTO1FBQ1RNLEdBQUc7SUFDTDtJQUNBTCxTQUFTO1FBQ1BELFNBQVM7UUFDVE0sR0FBRztRQUNISixZQUFZO1lBQ1ZLLFVBQVU7WUFDVkMsTUFBTTtRQUNSO0lBQ0Y7QUFDRjtBQUVlLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDZCxpREFBTUEsQ0FBQ2UsT0FBTztRQUNiQyxJQUFHO1FBQ0hDLFdBQVU7UUFDVkMsU0FBUTtRQUNSQyxhQUFZO1FBQ1pDLFVBQVU7WUFBRUMsTUFBTTtZQUFNQyxRQUFRO1FBQUk7UUFDcENDLFVBQVVwQjtrQkFFViw0RUFBQ3FCO1lBQUlQLFdBQVU7c0JBQ2IsNEVBQUNPO2dCQUFJUCxXQUFVOztrQ0FFYiw4REFBQ2pCLGlEQUFNQSxDQUFDeUIsRUFBRTt3QkFDUlIsV0FBVTt3QkFDVk0sVUFBVWI7d0JBQ1ZRLFNBQVE7d0JBQ1JDLGFBQVk7d0JBQ1pDLFVBQVU7NEJBQUVDLE1BQU07NEJBQU1DLFFBQVE7d0JBQUk7a0NBQ3JDOzs7Ozs7a0NBSUQsOERBQUN0QixpREFBTUEsQ0FBQzBCLENBQUM7d0JBQ1BULFdBQVU7d0JBQ1ZNLFVBQVViO3dCQUNWUSxTQUFRO3dCQUNSQyxhQUFZO3dCQUNaQyxVQUFVOzRCQUFFQyxNQUFNOzRCQUFNQyxRQUFRO3dCQUFJO3dCQUNwQ2YsWUFBWTs0QkFBRW9CLE9BQU87d0JBQUk7a0NBQzFCOzs7Ozs7a0NBS0QsOERBQUNIO3dCQUFJUCxXQUFVO2tDQUNaaEIscURBQWNBLENBQUMyQixHQUFHLENBQUMsQ0FBQ0MsWUFBWUMsc0JBQy9CLDhEQUFDNUIsdURBQWNBO2dDQUViMkIsWUFBWUE7Z0NBQ1pDLE9BQU9BOytCQUZGRCxXQUFXYixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVsQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxva3J0d1xcRGVza3RvcFxcUG9ydGZvbGlvXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxjb21wb25lbnRzXFxXb3JrRXhwZXJpZW5jZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IHdvcmtFeHBlcmllbmNlIH0gZnJvbSAnQC9saWIvZGF0YSc7XG5pbXBvcnQgRXhwZXJpZW5jZUNhcmQgZnJvbSAnLi9FeHBlcmllbmNlQ2FyZCc7XG5cbi8vIEFuaW1hdGlvbiB2YXJpYW50cyBmb3IgdGhlIHNlY3Rpb25cbmNvbnN0IGNvbnRhaW5lclZhcmlhbnRzID0ge1xuICBoaWRkZW46IHsgb3BhY2l0eTogMCB9LFxuICB2aXNpYmxlOiB7XG4gICAgb3BhY2l0eTogMSxcbiAgICB0cmFuc2l0aW9uOiB7XG4gICAgICBzdGFnZ2VyQ2hpbGRyZW46IDAuMixcbiAgICAgIGRlbGF5Q2hpbGRyZW46IDAuMSxcbiAgICB9LFxuICB9LFxufTtcblxuY29uc3QgdGl0bGVWYXJpYW50cyA9IHtcbiAgaGlkZGVuOiB7IFxuICAgIG9wYWNpdHk6IDAsIFxuICAgIHk6IDMwIFxuICB9LFxuICB2aXNpYmxlOiB7XG4gICAgb3BhY2l0eTogMSxcbiAgICB5OiAwLFxuICAgIHRyYW5zaXRpb246IHtcbiAgICAgIGR1cmF0aW9uOiAwLjYsXG4gICAgICBlYXNlOiAnZWFzZU91dCcsXG4gICAgfSxcbiAgfSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFdvcmtFeHBlcmllbmNlKCkge1xuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uc2VjdGlvblxuICAgICAgaWQ9XCJ3b3JrLWV4cGVyaWVuY2VcIlxuICAgICAgY2xhc3NOYW1lPVwicHktMjAgYmctYmFja2dyb3VuZFwiXG4gICAgICBpbml0aWFsPVwiaGlkZGVuXCJcbiAgICAgIHdoaWxlSW5WaWV3PVwidmlzaWJsZVwiXG4gICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlLCBhbW91bnQ6IDAuMiB9fVxuICAgICAgdmFyaWFudHM9e2NvbnRhaW5lclZhcmlhbnRzfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgey8qIFNlY3Rpb24gVGl0bGUgKi99XG4gICAgICAgICAgPG1vdGlvbi5oMlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC00eGwgbWQ6dGV4dC01eGwgZm9udC1ib2xkIHRleHQtdGV4dFByaW1hcnkgbWItNCB0ZXh0LWNlbnRlclwiXG4gICAgICAgICAgICB2YXJpYW50cz17dGl0bGVWYXJpYW50c31cbiAgICAgICAgICAgIGluaXRpYWw9XCJoaWRkZW5cIlxuICAgICAgICAgICAgd2hpbGVJblZpZXc9XCJ2aXNpYmxlXCJcbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUsIGFtb3VudDogMC4yIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgV29yayBFeHBlcmllbmNlXG4gICAgICAgICAgPC9tb3Rpb24uaDI+XG5cbiAgICAgICAgICA8bW90aW9uLnBcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC10ZXh0U2Vjb25kYXJ5IG1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyIG1iLTE2XCJcbiAgICAgICAgICAgIHZhcmlhbnRzPXt0aXRsZVZhcmlhbnRzfVxuICAgICAgICAgICAgaW5pdGlhbD1cImhpZGRlblwiXG4gICAgICAgICAgICB3aGlsZUluVmlldz1cInZpc2libGVcIlxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSwgYW1vdW50OiAwLjIgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIFByb2Zlc3Npb25hbCBqb3VybmV5IGFuZCBrZXkgY29udHJpYnV0aW9ucyBpbiBzb2Z0d2FyZSBkZXZlbG9wbWVudFxuICAgICAgICAgIDwvbW90aW9uLnA+XG5cbiAgICAgICAgICB7LyogRXhwZXJpZW5jZSBDYXJkcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAge3dvcmtFeHBlcmllbmNlLm1hcCgoZXhwZXJpZW5jZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPEV4cGVyaWVuY2VDYXJkXG4gICAgICAgICAgICAgICAga2V5PXtleHBlcmllbmNlLmlkfVxuICAgICAgICAgICAgICAgIGV4cGVyaWVuY2U9e2V4cGVyaWVuY2V9XG4gICAgICAgICAgICAgICAgaW5kZXg9e2luZGV4fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uc2VjdGlvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJ3b3JrRXhwZXJpZW5jZSIsIkV4cGVyaWVuY2VDYXJkIiwiY29udGFpbmVyVmFyaWFudHMiLCJoaWRkZW4iLCJvcGFjaXR5IiwidmlzaWJsZSIsInRyYW5zaXRpb24iLCJzdGFnZ2VyQ2hpbGRyZW4iLCJkZWxheUNoaWxkcmVuIiwidGl0bGVWYXJpYW50cyIsInkiLCJkdXJhdGlvbiIsImVhc2UiLCJXb3JrRXhwZXJpZW5jZSIsInNlY3Rpb24iLCJpZCIsImNsYXNzTmFtZSIsImluaXRpYWwiLCJ3aGlsZUluVmlldyIsInZpZXdwb3J0Iiwib25jZSIsImFtb3VudCIsInZhcmlhbnRzIiwiZGl2IiwiaDIiLCJwIiwiZGVsYXkiLCJtYXAiLCJleHBlcmllbmNlIiwiaW5kZXgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/WorkExperience.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/Button.tsx":
/*!**********************************!*\
  !*** ./components/ui/Button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Reusable Button Component with Framer Motion animations\nfunction Button({ variant = 'primary', onClick, href, children, className = '', type = 'button', disabled = false }) {\n    // Base styles for all buttons\n    const baseStyles = `\n    inline-flex items-center justify-center\n    px-6 py-3 rounded-lg font-medium text-sm\n    transition-all duration-200 ease-in-out\n    focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-background\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ${className}\n  `;\n    // Variant-specific styles\n    const variantStyles = {\n        primary: `\n      bg-accent text-background\n      hover:bg-accent/90\n      border border-accent\n    `,\n        outline: `\n      bg-transparent text-accent\n      border border-accent\n      hover:bg-accent hover:text-background\n    `\n    };\n    // Combined styles\n    const buttonStyles = `${baseStyles} ${variantStyles[variant]}`.trim();\n    // Animation props for Framer Motion\n    const animationProps = {\n        whileHover: disabled ? {} : {\n            scale: 1.05,\n            boxShadow: '0 0 8px rgba(100, 255, 218, 0.5)'\n        },\n        whileTap: disabled ? {} : {\n            scale: 0.95\n        },\n        transition: {\n            type: 'spring',\n            stiffness: 400,\n            damping: 17\n        }\n    };\n    // If href is provided, render as a Link\n    if (href) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: href,\n            className: \"inline-block\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                className: buttonStyles,\n                disabled: disabled,\n                ...animationProps,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    }\n    // Otherwise, render as a regular button\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n        type: type,\n        onClick: onClick,\n        className: buttonStyles,\n        disabled: disabled,\n        ...animationProps,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\janmejay-tiwari-portfolio\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/data.ts":
/*!*********************!*\
  !*** ./lib/data.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   certificates: () => (/* binding */ certificates),\n/* harmony export */   contactInfo: () => (/* binding */ contactInfo),\n/* harmony export */   education: () => (/* binding */ education),\n/* harmony export */   footerInfo: () => (/* binding */ footerInfo),\n/* harmony export */   heroData: () => (/* binding */ heroData),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   workExperience: () => (/* binding */ workExperience)\n/* harmony export */ });\n// Data & Asset Manifest - Single source of truth for all portfolio content\n// This file contains all the structured data used throughout the portfolio application\n// Type definitions for data structures\n// Hero Section Data (HERO-01 to HERO-09)\nconst heroData = {\n    name: \"Janmejay Tiwari\",\n    bio: \"A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.\",\n    profileImage: \"/assets/images/Profile.jpeg\",\n    buttons: {\n        viewWork: \"View Work\",\n        viewResume: \"View Resume\"\n    },\n    resumeLink: \"/assets/docs/Resume_Janmejay_Tiwari\",\n    socialLinks: {\n        github: \"https://github.com/Janmejay3108\",\n        linkedin: \"https://linkedin.com/in/janmejay-tiwari\",\n        twitter: \"https://x.com/Janmeja_y\"\n    }\n};\n// Work Experience Data (EXP-01 to EXP-11)\nconst workExperience = [\n    {\n        id: \"exp-01\",\n        companyName: \"Celebal Technologies\",\n        companyLogo: \"/assets/images/logo_celebal.svg\",\n        role: \"React Developer Intern\",\n        timeline: \"May 2025 - July 2025\",\n        details: [\n            \"Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.\",\n            \"Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS.\"\n        ],\n        certificateLink: \"https://drive.google.com/file/d/15YBIy42FxAL-MBu8AApibsSMeBCZ208e/view?usp=sharing\"\n    },\n    {\n        id: \"exp-02\",\n        companyName: \"1stop.ai\",\n        companyLogo: \"/assets/images/logo_1stopai.svg\",\n        role: \"Frontend developer Intern\",\n        timeline: \"March 2024 - April 2024.\",\n        details: [\n            \"Collaborated with a team to develop responsive web application using MERN stack.\",\n            \"Implemented frontend components using React.js.\",\n            \"Assisted in database design and implementation with Firebase.\",\n            \"Integrated AWS S3 for media storage and content delivery.\"\n        ],\n        certificateLink: \"https://drive.google.com/file/d/1Hi-eA_mT2DMZPnIUgGvEWqbPAtC50tKs/view?usp=sharing\"\n    }\n];\n// Project Work Data (PROJ-01 to PROJ-10)\nconst projects = [\n    {\n        id: \"proj-01\",\n        name: \"Web accessibility analyser\",\n        purpose: \"A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.\",\n        techStack: \"React.js, Node.js, Express.js, Firebase, Firestore, axe-core, Playwright, Tailwind CSS\",\n        learnings: \"Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.\",\n        githubLink: \"https://github.com/Janmejay3108/Accessibility-analyzer\"\n    },\n    {\n        id: \"proj-02\",\n        name: \"Secure File Sharing Platform\",\n        purpose: \"A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.\",\n        techStack: \"React.js, Node.js, Express.js, Vite, Tailwind CSS, i18next, Multer, Docker\",\n        learnings: \"Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.\",\n        githubLink: \"https://github.com/Janmejay3108/File_Transfer_application\"\n    }\n];\n// Certificates Data (CERT-01 to CERT-18)\nconst certificates = [\n    {\n        id: \"cert-01\",\n        name: \"AWS Cloud Solutions Architect\",\n        issuerName: \"Amazon Web Services\",\n        issuerLogo: \"/assets/images/logo_aws.svg\"\n    },\n    {\n        id: \"cert-02\",\n        name: \"Full Stack Developer\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-03\",\n        name: \"Database and SQL for data science with Python\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-04\",\n        name: \"RAG and Agentic AI\",\n        issuerName: \"IBM\",\n        issuerLogo: \"/assets/images/logo_ibm.svg\"\n    },\n    {\n        id: \"cert-05\",\n        name: \"Artificial Intelligence Essentials\",\n        issuerName: \"Google\",\n        issuerLogo: \"/assets/images/logo_google.svg\"\n    },\n    {\n        id: \"cert-06\",\n        name: \"Convolutional Neural Networks\",\n        issuerName: \"Deeplearning.AI\",\n        issuerLogo: \"/assets/images/logo_deeplearning_ai.svg\"\n    }\n];\n// Education Data (EDU-01 to EDU-04)\nconst education = {\n    degree: \"BTech in Electronics and Communication Engineering\",\n    institution: \"Institute of Engineering and Management, Kolkata\",\n    timeline: \"2022-2026\",\n    detail: \"CGPA: 9.38\"\n};\n// Contact Information (CONTACT-01 to CONTACT-02)\nconst contactInfo = {\n    email: \"<EMAIL>\",\n    phone: \"+91 9163083482\"\n};\n// Footer Information (FOOTER-01)\nconst footerInfo = {\n    name: \"Janmejay Tiwari\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/data.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q29rcnR3JTVDJTVDRGVza3RvcCU1QyU1Q1BvcnRmb2xpbyU1QyU1Q2phbm1lamF5LXRpd2FyaS1wb3J0Zm9saW8lNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxva3J0d1xcXFxEZXNrdG9wXFxcXFBvcnRmb2xpb1xcXFxqYW5tZWpheS10aXdhcmktcG9ydGZvbGlvXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cokrtw%5C%5CDesktop%5C%5CPortfolio%5C%5Cjanmejay-tiwari-portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/react-icons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cokrtw%5CDesktop%5CPortfolio%5Cjanmejay-tiwari-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();